$highlite1: #b95e04;

$darkgrey: #666;
$basetextfont: arial, sans-serif;

@mixin basefont {
  font-family: $basetextfont;
  font-weight: normal;
}

.mailPage {
  @include basefont;
  font-size: 14px;

  h1 {
    color: $highlite1;
    font-size: 21px;
  }

  h2 {
    color: $highlite1;
    font-size: 18px;
  }

  h3 {
    color: $darkgrey;
    font-size: 14px;
  }

  .resultLabel {
    color: $darkgrey;
    font-weight: bold;
    overflow: hidden;
  }

  .resultValue {
    font-weight: normal;
    overflow: hidden;
    width: 67%;
  }

  table.fields {
    @include basefont;
    border-collapse: collapse;
    width: 100%;
  }

  table.fields th,
  table.fields td {
    @include basefont;
    font-size: 12px;
    padding: 3px 6px 3px 0;
    vertical-align: top;
  }

  table.fields table.cluster th,
  table.fields table.cluster td {
    @include basefont;
    font-size: 12px;
    padding: 0 6px 3px 0;
    vertical-align: top;
  }
}

