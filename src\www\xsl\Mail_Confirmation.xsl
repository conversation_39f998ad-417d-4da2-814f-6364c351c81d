<?xml version="1.0" encoding="utf-8" ?>
<!--
  /**
   * (c) InfoProjects bv. - www.infoprojects.nl
   */
 -->
<xsl:stylesheet version="1.0"
  xmlns:frm="http://www.iprox.nl/ns/forms/"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:d1p1="http://www.iprox.nl/ns/forms/result/"
  exclude-result-prefixes="frm xsi d1p1">

  <xsl:output method="html" version="4.0" encoding="utf-8" doctype-public="-//W3C//DTD HTML 4.01 Transitional//EN" indent="yes"/>

  <xsl:include href="Util.xsl" />

  <xsl:param name="Receipt_Intro" />

  <xsl:template match="/">
    <html>
      <head>
      </head>
      <body>
        <div class="mailPage">
          <xsl:apply-templates select="frm:Form" />
          <xsl:apply-templates select="frm:Form/descendant::frm:Field[contains(@Channels, 'Receipt')]" mode="Summary"/>
        </div>
      </body>
    </html>
  </xsl:template>

  <xsl:template match="frm:Field" mode="Summary">
    <div>
      <xsl:apply-templates select="self::frm:Field" />
    </div>
  </xsl:template>

  <xsl:template match="*[@Excluded or @ExcludeFromSubmit or frm:Precondition/frm:Condition[@Result='False']]" mode="Summary" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit or frm:Precondition/frm:Condition[@Result='False']]" priority="3" />

  <xsl:template match="frm:Field">
    <xsl:value-of select="frm:Label" /><xsl:text>: </xsl:text>
    <xsl:choose>
      <xsl:when test="count(frm:Values/frm:Value) > 1">
        <ul>
          <xsl:for-each select="frm:Values/frm:Value">
            <li><xsl:value-of select="@Display"/></li>
          </xsl:for-each>
        </ul>
      </xsl:when>
      <xsl:otherwise>
        <xsl:value-of select="frm:Values/frm:Value/@Display"/>
        <xsl:if test="../frm:LabelAfter">
          <xsl:value-of select="concat(' ', ../frm:LabelAfter)" />
        </xsl:if>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="frm:Form">
    <h2>
      <xsl:value-of select="frm:Label"/>
    </h2>
    <xsl:choose>
      <xsl:when test="$Receipt_Intro!=''">
        <xsl:value-of select="$Receipt_Intro" disable-output-escaping="yes" />
      </xsl:when>
      <xsl:otherwise>
        <p>Uw formulier is (naar het zich laat aanzien) in goede orde verzonden.</p>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

</xsl:stylesheet>
