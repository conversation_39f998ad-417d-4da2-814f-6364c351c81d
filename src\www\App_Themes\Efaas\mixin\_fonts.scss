@mixin font-size($px) {
  font-size: #{$px / 16}rem;
}

body {

  h1,
  h2,
  h3 {
    color: $headercolor;
    line-height: 1.2em;
  }

  h1 {
    @include headerfont;
    @include font-size(34);
    line-height: 1.2em;
  }

  h2 {
    @include headerfont;
    @include font-size(24);
  }

  h3 {
    @include headerfont;
    @include font-size(18);
  }

  h4 {
    @include standard-font;
    @include font-size(18);
  }

  h5 {
    @include standard-font;
    @include font-size(18);
  }

  h6 {
    @include standard-font;
    @include font-size(14);
  }


  p,
  ul,
  ol {
    @include standard-font;
  }

  a {
    color: $black;
    text-decoration: none;

    &:hover,
    &:focus {
      color: $highlite1;
    }
  }

  .z-content a {
    &:hover,
    &:focus {
      text-decoration: underline;
    }
  }
}
