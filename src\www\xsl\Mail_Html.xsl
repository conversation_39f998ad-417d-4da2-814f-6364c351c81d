<?xml version="1.0" encoding="utf-8" ?>
<!--
  /**
   * (c) InfoProjects bv. - www.infoprojects.nl
   */
 -->
<xsl:stylesheet version="1.0"
  xmlns:frm="http://www.iprox.nl/ns/forms/"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  exclude-result-prefixes="frm xsi">

  <xsl:output method="html" version="4.0" encoding="utf-8" doctype-public="-//W3C//DTD HTML 4.01 Transitional//EN" indent="yes"/>

  <xsl:include href="Util.xsl" />

  <xsl:param name="Summary_Verified" />

  <xsl:template match="/">
    <html>
      <head>
      </head>
      <body>
        <div class="mailPage">
          <xsl:apply-templates select="frm:Form" />
          <xsl:if test="frm:Form/descendant::frm:Field[contains(@Channels, 'Email')]">
            <table class="fields">
              <xsl:apply-templates select="frm:Form/descendant::frm:Field[contains(@Channels, 'Email')]" mode="Summary"/>
            </table>
          </xsl:if>
        </div>
      </body>
    </html>
  </xsl:template>

  <xsl:template match="frm:Field" mode="Summary">
    <xsl:apply-templates select="self::frm:Field" />
  </xsl:template>

  <xsl:template match="frm:Form">
    <h1>
      <xsl:value-of select="frm:Label"/>
    </h1>
    <xsl:apply-templates select="frm:Steps/frm:Step" />
  </xsl:template>

  <xsl:template match="frm:Step">
    <xsl:variable name="shownBlocks" select="frm:Blocks/frm:Block[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']) and frm:Fields/frm:Field[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOutput)) or frm:Precondition/frm:Condition[@Result='False']]]" />
    <xsl:if test="$shownBlocks">
      <div class="step">
        <h2>
          <xsl:value-of select="frm:Label"/>
        </h2>
        <xsl:apply-templates select="$shownBlocks" />
      </div>
      <xsl:apply-templates select="frm:Steps/frm:Step" />
    </xsl:if>
  </xsl:template>

  <xsl:template match="frm:Block">
    <xsl:choose>
      <xsl:when test="@xsi:type = 'FieldsBlock' and (frm:MinimumCount or frm:MaximumCount)">
        <xsl:apply-templates select="frm:Blocks/frm:Block" />
      </xsl:when>
      <xsl:otherwise>
        <xsl:variable name="containsDefaultValue">
          <xsl:choose>
            <xsl:when test="not(@xsi:type = 'SkpAddressBlock')">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field/frm:Rows/frm:Row/frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:otherwise>true</xsl:otherwise>
          </xsl:choose>
        </xsl:variable>

        <div class="block">
          <xsl:if test="not(HideTitle)">
            <h3>
              <xsl:value-of select="frm:Label" />
            </h3>
          </xsl:if>
          <xsl:if test="$containsDefaultValue = 'true'">
            <div>
              <xsl:value-of select="$Summary_Verified" />
            </div>
          </xsl:if>
          <table class="fields">
            <xsl:apply-templates select="frm:Fields/frm:Field" />
          </table>
        </div>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster-header" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster-footer" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" priority="3" />

  <xsl:template match="frm:Field">
    <xsl:choose>
      <xsl:when test="count(frm:Fields/frm:Field) > 0">
        <tr>
          <xsl:attribute name="class">
            <xsl:apply-templates select="@ValidationResult" />
            <xsl:if test="@DisplayHint">
              <xsl:value-of select="concat(' displayhint-', @DisplayHint)"/>
            </xsl:if>
            <xsl:if test="@TextInputType">
              <xsl:value-of select="concat(' inputtype-', @TextInputType)"/>
            </xsl:if>
          </xsl:attribute>
          <td class="resultLabel" colspan="count(frm:Fields/frm:Field)">
            <xsl:value-of select="frm:Label" />
          </td>
        </tr>
        <tr>
          <xsl:attribute name="class">
            <xsl:text>field</xsl:text>
            <xsl:apply-templates select="@ValidationResult" />
          </xsl:attribute>
          <td class="resultValue">
            <xsl:if test="count(frm:Rows/frm:Row) > 0" >
              <table class="cluster">
                <thead>
                  <tr>
                    <xsl:apply-templates select="frm:Fields/frm:Field" mode="cluster-header" />
                  </tr>
                </thead>
                <tbody>
                  <xsl:apply-templates select="frm:Rows/frm:Row/frm:Fields" mode="cluster" />
                </tbody>
                <xsl:if test="count(frm:TotalFields/frm:FieldID) > 0" >
                  <tfoot class="total">
                    <tr>
                      <xsl:apply-templates select="frm:Fields/frm:Field" mode="cluster-footer" />
                    </tr>
                  </tfoot>
                </xsl:if>
              </table>
            </xsl:if>
          </td>
        </tr>
      </xsl:when>
      <xsl:otherwise>
        <tr>
          <xsl:attribute name="class">
            <xsl:text>field</xsl:text>
            <xsl:apply-templates select="@ValidationResult" />
            <xsl:if test="@DisplayHint">
              <xsl:value-of select="concat(' displayhint-', @DisplayHint)"/>
            </xsl:if>
            <xsl:if test="@TextInputType">
              <xsl:value-of select="concat(' inputtype-', @TextInputType)"/>
            </xsl:if>
          </xsl:attribute>
          <td class="resultLabel">
            <xsl:value-of select="frm:Label" />
          </td>
          <td class="resultValue">
            <xsl:apply-templates select="frm:Values" />
          </td>
        </tr>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="frm:Fields" mode="cluster">
    <tr>
      <xsl:apply-templates select="frm:Field" mode="cluster" />
    </tr>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster-header">
    <th>
      <xsl:value-of select="frm:Label" />
    </th>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster-footer">
    <th>
      <xsl:if test="@TextInputType">
        <xsl:attribute name="class">
          <xsl:value-of select="concat('inputtype-', @TextInputType)"/>
        </xsl:attribute>
      </xsl:if>
      <xsl:if test="../../frm:TotalFields/frm:FieldID[substring(., 4) = current()/frm:ID]">
        <xsl:apply-templates select="frm:Values" />
      </xsl:if>
    </th>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster">
    <td>
      <xsl:apply-templates select="frm:Values" />
    </td>
  </xsl:template>

  <xsl:template match="frm:Values">
    <xsl:choose>
      <xsl:when test="count(frm:Value) > 1">
        <ul>
          <xsl:for-each select="frm:Value">
            <li><xsl:value-of select="@Display"/></li>
          </xsl:for-each>
        </ul>
      </xsl:when>
      <xsl:otherwise>
        <xsl:value-of select="frm:Value/@Display"/>
        <xsl:if test="../frm:LabelAfter">
          <xsl:value-of select="concat(' ', ../frm:LabelAfter)" />
        </xsl:if>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

</xsl:stylesheet>
