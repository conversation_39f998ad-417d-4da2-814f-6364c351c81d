@mixin textblock {
  background-color: $accent4;
  border: 4px solid #fff;
  border-radius: 12px;
  margin: 5px $grid-gutter1 ($grid-gutter1 * 2);
  padding: 1em .75em .25em;

  *:first-child {
    margin-top: 0;
    padding-top: 0;
  }

  .label {
    font-size: 1em;
    margin-bottom: $grid-gutter1;
  }

  .input {
    ul {
      margin-bottom: .5em;
      margin-top: .5em;
      padding-left: 40px;

      li {
        list-style-type: disc;
      }
    }

    h3,
    h4 {
      margin-bottom: 0;

      + p {
        margin-top: 0;
      }
    }

    *:first-child {
      margin-top: 0;
      padding-top: 0;
    }
  }

  a {
    text-decoration: underline;
  }
}

@mixin hyphenate {
  hyphens: auto;
  word-break: break-all;
}

@mixin visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

@mixin visually {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

.visuallyhidden,
.tekstbrowser {
  @include visuallyhidden;

  &.focusable:active,
  &.focusable:focus {
    @include visually;
  }
}

.visually {
  @include visually;
}
