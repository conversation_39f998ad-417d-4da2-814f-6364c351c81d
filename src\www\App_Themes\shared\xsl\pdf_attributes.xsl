<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet version="1.0" xmlns="Aspose.Pdf" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:variable name="font-name" select="'Arial'" />
  <xsl:variable name="font-name-bold" select="'Arial'" />
  <xsl:variable name="font-name-bold-bold" select="'true'" />
  <xsl:variable name="heading-font-name" select="'Arial'" />
  <xsl:variable name="heading-font-bold" select="'true'" />
  <xsl:variable name="label-font-name" select="'Arial'" />
  <xsl:variable name="label-font-bold" select="'true'" />
  <xsl:variable name="values-background-color" select="'white'" />
  <xsl:variable name="divider-color" select="'gray 153'" />
  <xsl:variable name="divider-line-width" select="'1'" />

  <xsl:attribute-set name="page">
    <xsl:attribute name="PageMarginTop">2cm</xsl:attribute>
    <xsl:attribute name="PageMarginBottom">2cm</xsl:attribute>
    <xsl:attribute name="PageMarginLeft">2cm</xsl:attribute>
    <xsl:attribute name="PageMarginRight">2cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text">
    <xsl:attribute name="FontName"><xsl:value-of select="$font-name" /></xsl:attribute>
    <xsl:attribute name="FontSize">9</xsl:attribute>
    <xsl:attribute name="LineSpacing">4.0</xsl:attribute>
    <xsl:attribute name="IsSpaced">true</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="bold">
    <xsl:attribute name="FontName"><xsl:value-of select="$font-name-bold" /></xsl:attribute>
    <xsl:attribute name="IsTrueTypeFontBold">true</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="italic">
    <xsl:attribute name="IsTrueTypeFontItalic">true</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="larger">
    <xsl:attribute name="FontSize">10</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="small">
    <xsl:attribute name="FontSize">8</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="smaller">
    <xsl:attribute name="FontSize">7</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="small-spacing">
    <xsl:attribute name="LineSpacing">2.5</xsl:attribute>
    <xsl:attribute name="IsSpaced">true</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="center">
    <xsl:attribute name="Alignment">Center</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-info-box">
    <xsl:attribute name="PaddingLeft">0.2cm</xsl:attribute>
    <xsl:attribute name="PaddingTop">0.1cm</xsl:attribute>
    <xsl:attribute name="BackgroundColor">gray 204</xsl:attribute>
    <xsl:attribute name="BoxHorizontalAlignment">Left</xsl:attribute>
    <xsl:attribute name="Width">11.5cm</xsl:attribute>
    <xsl:attribute name="Height">1.1cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-dont-send-box">
    <xsl:attribute name="PaddingTop">0.4cm</xsl:attribute>
    <xsl:attribute name="BackgroundColor">gray 204</xsl:attribute>
    <xsl:attribute name="Width">5.0cm</xsl:attribute>
    <xsl:attribute name="Height">1.1cm</xsl:attribute>
    <xsl:attribute name="Left">12cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-dont-send-box-text">
    <xsl:attribute name="FontName"><xsl:value-of select="$heading-font-name" /></xsl:attribute>
    <xsl:attribute name="FontSize">10</xsl:attribute>
    <xsl:attribute name="IsTrueTypeFontBold"><xsl:value-of select="$heading-font-bold" /></xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-heading-table">
    <xsl:attribute name="ColumnWidths">17cm</xsl:attribute>
    <xsl:attribute name="MarginTop">0.5cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-heading-cell">
    <xsl:attribute name="PaddingBottom">0.2cm</xsl:attribute>
    <xsl:attribute name="PaddingTop">0.1cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-heading">
    <xsl:attribute name="LabelWidth">0</xsl:attribute>
    <xsl:attribute name="MarginBottom">0cm</xsl:attribute>
    <xsl:attribute name="MarginTop">1.8cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="step-before">
    <xsl:attribute name="PaddingBottom">0.35cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="step-heading-cell">
    <xsl:attribute name="PaddingTop">0.1cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="step-heading">
    <xsl:attribute name="LabelWidth">0</xsl:attribute>
    <xsl:attribute name="MarginBottom">0.1cm</xsl:attribute>
    <xsl:attribute name="MarginTop">0.2cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="step-after">
    <xsl:attribute name="PaddingBottom">0.25cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="block-heading">
    <xsl:attribute name="LabelWidth">0</xsl:attribute>
    <xsl:attribute name="MarginBottom">0.1cm</xsl:attribute>
    <xsl:attribute name="MarginTop">0.2cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="h0">
    <xsl:attribute name="FontName"><xsl:value-of select="$heading-font-name" /></xsl:attribute>
    <xsl:attribute name="FontSize">14</xsl:attribute>
    <xsl:attribute name="IsTrueTypeFontBold"><xsl:value-of select="$heading-font-bold" /></xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="h1">
    <xsl:attribute name="FontName"><xsl:value-of select="$heading-font-name" /></xsl:attribute>
    <xsl:attribute name="FontSize">12</xsl:attribute>
    <xsl:attribute name="IsTrueTypeFontBold"><xsl:value-of select="$heading-font-bold" /></xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="h2">
    <xsl:attribute name="FontName"><xsl:value-of select="$heading-font-name" /></xsl:attribute>
    <xsl:attribute name="FontSize">10</xsl:attribute>
    <xsl:attribute name="IsTrueTypeFontBold"><xsl:value-of select="$heading-font-bold" /></xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="table">
    <xsl:attribute name="ColumnWidths">8cm 9cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="row">
    <xsl:attribute name="VerticalAlignment">Top</xsl:attribute>
    <xsl:attribute name="IsBroken">false</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="cell">
    <xsl:attribute name="VerticalAlignment">Top</xsl:attribute>
    <xsl:attribute name="PaddingTop">0.05cm</xsl:attribute>
    <xsl:attribute name="PaddingBottom">0.10cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="header">
    <xsl:attribute name="PaddingRight">0.5cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="label">
    <xsl:attribute name="FontName"><xsl:value-of select="$label-font-name" /></xsl:attribute>
    <xsl:attribute name="IsTrueTypeFontBold"><xsl:value-of select="$label-font-bold" /></xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="sublabel">
    <xsl:attribute name="Color">gray 102</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="values">
    <xsl:attribute name="BackgroundColor"><xsl:value-of select="$values-background-color" /></xsl:attribute>
    <xsl:attribute name="PaddingLeft">0.2cm</xsl:attribute>
    <xsl:attribute name="PaddingRight">0.2cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="cluster-values">
    <xsl:attribute name="BackgroundColor"><xsl:value-of select="$values-background-color" /></xsl:attribute>
    <xsl:attribute name="PaddingLeft">0.05cm</xsl:attribute>
    <xsl:attribute name="PaddingRight">0.05cm</xsl:attribute>
    <xsl:attribute name="PaddingBottom">0.10cm</xsl:attribute>
  </xsl:attribute-set>

</xsl:stylesheet>
