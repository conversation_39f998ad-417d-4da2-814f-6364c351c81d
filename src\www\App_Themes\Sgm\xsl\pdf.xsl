<xsl:stylesheet version="1.0"
  xmlns:frm="http://www.iprox.nl/ns/forms/"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:forms="urn:forms"
  xmlns="Aspose.Pdf"
  exclude-result-prefixes="frm xsi forms">

  <xsl:import href="../../shared/xsl/pdf_document.xsl" />

  <xsl:variable name="font-name" select="'Open Sans Regular'" />
  <xsl:variable name="heading-font-name" select="'Open Sans Bold'" />
  <xsl:variable name="heading-font-bold" select="false" />
  <xsl:variable name="values-background-color" select="'#eeeeee'" />
  <xsl:variable name="divider-color" select="'#f59501'" />
  <xsl:variable name="fullWidthThreshold" select="750" />

  <xsl:attribute-set name="page">
    <xsl:attribute name="PageMarginTop">3cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:template name="page-header">
    <Header MarginTop="1cm" MarginLeft="0">
      <Table Alignment="Left" ColumnWidths="8cm 9cm">
        <Row>
          <Cell PaddingTop="0.1cm">
            <Text>
              <Segment>
                <Image Alignment="Bottom" Type="Png" ID="HeaderPicture" OpenType="File" FixWidth="2.6cm" File="{$themeFolder}\images\assets\sgm-logo.png">
                  <xsl:text>Logo Schadefonds Geweldsmisdrijven</xsl:text>
                </Image>
              </Segment>
            </Text>
          </Cell>
          <Cell>
            <Text Alignment="Right" xsl:use-attribute-sets="smaller small-spacing">
              <Segment>Schadefonds Geweldsmisdrijven #$NL</Segment>
              <Segment>Kneuterdijk 1 #$NL</Segment>
              <Segment>2501 CB Den Haag #$NL</Segment>
              <Segment>www.schadefonds.nl</Segment>
            </Text>
          </Cell>
        </Row>
      </Table>
    </Header>
  </xsl:template>

  <xsl:template name="info-box">
  </xsl:template>

  <xsl:attribute-set name="form-dont-send-box">
    <xsl:attribute name="PaddingTop">0.25cm</xsl:attribute>
    <xsl:attribute name="Width">17.0cm</xsl:attribute>
    <xsl:attribute name="Height">0.85cm</xsl:attribute>
    <xsl:attribute name="Left">0cm</xsl:attribute>
    <xsl:attribute name="Top">0.25cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-heading-table">
    <xsl:attribute name="MarginTop">1.5cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="block-heading">
    <xsl:attribute name="MarginBottom">0.1cm</xsl:attribute>
    <xsl:attribute name="MarginTop">0.4cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-heading-cell">
    <xsl:attribute name="BackgroundColor">#0e70b6</xsl:attribute>
    <xsl:attribute name="PaddingBottom">0.2cm</xsl:attribute>
    <xsl:attribute name="PaddingLeft">0.2cm</xsl:attribute>
    <xsl:attribute name="PaddingRight">0.1cm</xsl:attribute>
    <xsl:attribute name="PaddingTop">0.1cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="form-heading">
    <xsl:attribute name="Color">White</xsl:attribute>
    <xsl:attribute name="MarginBottom">0cm</xsl:attribute>
    <xsl:attribute name="MarginTop">0cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="h2">
    <xsl:attribute name="Color">#0e70b6</xsl:attribute>
  </xsl:attribute-set>

</xsl:stylesheet>
