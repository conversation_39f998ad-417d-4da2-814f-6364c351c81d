@import "vars";
@import "fonts";

button,
input,
optgroup,
select,
textarea {
  font-family: $basetextfont;
}

.saveControl {
  background-color: $formbackground;
  border: $borderwidth solid $bordercolor;
  border-radius: $borderradius;
  height: auto !important;
  line-height: 1.5rem !important;
  padding: 0 !important;
  width: 100% !important;

  > p,
  > a {
    margin-left: $grid-gutter;
    margin-right: $grid-gutter;
  }
}

.formControl {
  background-color: $formbackground;
  border: $borderwidth solid $bordercolor;
  border-radius: $borderradius;
  line-height: 1.5rem;
  margin-bottom: 100px;
  margin-top: ($grid-gutter * 2.5);
  padding: 0 0 ($grid-gutter * 2.5);
  width: 100%;

  &#Main_ErrorBody {
    p {
      line-height: 1.2em;
      min-height: 40px;
      padding-left: 19px;
      padding-top: 1px;
    }

    a.action-back {
      @include font-size(16);
      background: url("#{$icons-img-path}pijllinks.svg") 0 0 no-repeat;
      background-size: 20px 20px;
      display: inline-block;
      line-height: 1.2em;
      min-height: 40px;
      overflow: hidden;
      padding-left: 19px;
      padding-top: 1px;
      width: calc(100% - 19px);

      &:hover {
        background: url("#{$icons-img-path}pijllinks-hilite.svg") 0 0 no-repeat;
        background-size: 20px 20px;
      }
    }
  }

  > h1 {
    margin: 0 $grid-gutter;
  }

  h2 {
    margin: 0 $grid-gutter $grid-gutter;
  }

  h2:first-of-type {
    @include font-size(24);
    margin: 0 ($grid-gutter * -1) $grid-gutter;
    padding: .3em (#{$grid-gutter * 2});
    width: calc(100% - (#{$grid-gutter * 2}));
  }

  > * {
    margin: 0;
  }

  .block-buttons-add,
  .block-buttons-remove {
    padding: 5px $grid-gutter ($grid-gutter * 2) $grid-gutter;
    text-align: right;

    input[type="submit"] {
      @include buttonstyle;
    }

    input[type="submit"] {
      background-position: 100% 50%;
      background-repeat: no-repeat;
      background-size: 24px 24px;
      padding-right: 30px;

      &:not(:last-child) {
        margin-right: 10px;
      }

      &:hover {
        background-size: 24px 24px;
        padding-right: 30px;
      }
    }
  }

  .block-buttons-add {
    input[type="submit"] {
      background-image: url("#{$icons-img-path}plus.svg");

      &:hover {
        background-image: url("#{$icons-img-path}plus-hilite.svg");
      }
    }
  }

  .block-buttons-remove {
    input[type="submit"] {
      background-image: url("#{$icons-img-path}min.svg");

      &:hover {
        background-image: url("#{$icons-img-path}min-hilite.svg");
      }
    }
  }

  legend + .block-buttons {
    position: absolute;
    right: 10px;
    top: -4px;
    z-index: $z-base-elevated;

    input[type="submit"] {
      @include buttonstyle;
    }

    input[type="submit"] {
      background-image: url("#{$icons-img-path}min.svg");
      background-position: 100% 50%;
      background-repeat: no-repeat;
      background-size: 24px 24px;
      padding-right: 30px;

      &:hover {
        background-image: url("#{$icons-img-path}min-hilite.svg");
        background-size: 24px 24px;
        padding-right: 30px;
      }
    }

  }

  fieldset {
    border: 0 none !important;
    clear: both;
    display: block;
    margin-left: 0;
    margin-right: 0;
    padding: 0;
    position: relative;
  }

  .fieldsBlock {
    padding-bottom: ($grid-gutter * 2);
  }

  .fieldsBlock > fieldset > h3,
  .fieldsBlock > fieldset > legend,
  > fieldset > legend {
    @include headerfont;
    color: $fieldset-header-color;
    font-size: 1.313em;
    margin: 0;
    padding: $grid-gutter;
  }

  .fieldsBlock > fieldset > legend + h3 {
    padding-top: 0;
  }


  .blockResults {
    @include textblock;
    background: url("#{$icons-img-path}kruis-rood.svg") 12px 12px no-repeat $superlightgrey;
    background-size: 30px 30px;
    border-color: $highlite1;
    padding-left: 55px;

    .label {
      display: none;
    }

    * {
      color: $verydarkgrey;
    }

    ul {
      margin: 0;
      padding-bottom: .5em;

      li {
        color: $verydarkgrey;
        list-style-type: disc;
        margin-left: 20px;

        .fieldname::after {
          content: ": ";
        }
      }
    }
  }
}

.meldingen-next {
  .text {
    display: none;
  }
}

.melding {
  @include textblock;
  margin-right: 0;

  &:first-child {
    margin-top: 20px;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &.melding-mededeling {
    border-color: $middlegrey;

    h2 {
      color: $middlegrey;
    }
  }

  &.melding-storing {
    background: url("#{$icons-img-path}kruis-rood.svg") 12px 12px no-repeat $superlightgrey;
    background-size: 20px 20px;
    border-color: $highlite1;
    padding-left: 60px;

    h2 {
      color: $highlite1;
    }
  }

  &.melding-onderhoud {
    background: url("#{$icons-img-path}attentie-lgrijs-bg-dgrijs.svg") 12px 16px no-repeat;
    background-size: 18px 18px;
    padding-left: 60px;

    h2 {
      color: $black;
    }
  }
}

div.digid-cancel,
div.digid-error,
div.eherkenning-cancel,
div.eherkenning-error,
div.eidas-cancel,
div.eidas-error,
.skp-error,
.submit-blocked,
.signing-cancel,
.signing-error {
  @include textblock;
  background: url("#{$icons-img-path}kruis-rood.svg") 12px 12px no-repeat $superlightgrey;
  background-size: 40px 40px;
  border-color: $highlite1;
  color: $verydarkgrey;
  min-height: 44px;
  padding-left: 60px !important;
}

.signingBlockText {
  margin: 5px 10px 20px;

  .signing-error {
    margin: 0 0 1em;
    padding-top: 1em;
  }
}

.signingBtn {
  padding: 5px $grid-gutter ($grid-gutter * 2);

  input[type="submit"] {
    @include buttonstyle;

    &:hover {
      background-color: $lightgrey;
      border-radius: $borderradius;
    }
  }
}

.signingBlockText {
  margin: 5px 10px 20px;

  .signing-error {
    margin: 0 0 1em;
    padding-top: 1em;
  }
}

.has-digid,
.has-eherkenning,
.has-eidas {
  legend {
    display: none;
  }
}

.identification {
  @include textblock;

  margin-bottom: 20px;
  margin-top: 20px;
  min-height: 60px;

  .digidLogo,
  .eherkenningLogo,
  .eidasLogo {
    margin: 0 0 0 5px;
  }

  .digid-message,
  .eherkenning-message,
  .eidas-message {
    display: inline-block;
  }

  .signing-error {
    margin: 1em 0 0;
    padding-top: 1em;
  }

  p:first-of-type {
    margin-top: 0;
  }
}

.digid-message {
  .digid-cancel {
    padding-top: 1em;
  }
}

.eherkenning-message {
  .eherkenning-cancel {
    padding-top: 1em;
  }
}

.eidas-message {
  .eidas-cancel {
    padding-top: 1em;
  }
}

.identification-digid {
  border-color: $digid;
}

.identification-eherkenning {
  border-color: $eherkenning;
}

.identification-eidas {
  border-color: $eidas;
}

.formField.digidOKBtn,
.formField.eherkenningOKBtn,
.formField.eidasOKBtn,
.formField.signingBtn {
  align-items: center;
  display: flex;
  padding: 5px 0 ($grid-gutter * 2);

  label {
    margin-bottom: 0;
  }

  div.input {
    margin-left: auto;
  }

  input[type="submit"] {
    @include buttonstyle;
  }

  input[type="submit"] {
    color: #fff;
    display: block;

    &:hover {
      background-color: $lightgrey;
      border: $borderwidth solid $formgrey;
      border-radius: $borderradius;
    }
  }

  .input {
    margin: -1px 0 0 10px;
  }
}

.digidOKBtn input[type="submit"] {
  background-color: $digid;
  color: #000;
}

.eherkenningOKBtn input[type="submit"] {
  background-color: $eherkenning;
  color: #fff;
}

.eidasOKBtn input[type="submit"] {
  background-color: $eidas;
  color: #fff;
}

.formField {
  padding: 5px $grid-gutter ($grid-gutter * 2) $grid-gutter;
  position: relative;

  &.typeSelectionField,
  &.typeCluster {
    legend {
      @include visuallyhidden;
    }
  }

  label {
    @include headerfont;
    @include font-size(18);
    color: $darkgrey;
    display: inline-block;
    margin-bottom: 10px;
    padding-right: 30px;
  }

  .label {
    @include headerfont;
    @include font-size(18);
    color: $darkgrey;
    display: block;
    margin-bottom: 10px;
    padding-right: 30px;
    position: relative;
  }

  h4.label {
    @include font-size(18);
    margin: 0 0 $grid-gutter;
  }

  &.hidelabel {
    label,
    .label {
      @include visuallyhidden;
    }

    ul {
      label,
      .label {
        @include visually;
      }
    }
  }

  .input {
    position: relative;
  }

  .volatile {
    display: block;
  }

  .hint-input-placeholder,
  .explanation .inner,
  .volatile {
    @include basefont;
    background-color: $tooltip-background-color;
    border-left: $tooltip-border-width $tooltip-border-style $tooltip-border-color;
    display: block;
    font-size: 0.9em;
    margin-bottom: 15px;
    margin-top: -5px;
    padding: $tooltip-padding-vertical $tooltip-padding-horizontal;

    > *:first-child {
      margin-top: 0;
    }

    > *:last-child {
      margin-bottom: 0;
    }

    ul,
    ol {
      padding-left: 2em;
      
      li {
        list-style-type: disc;
      }
    }
  }

  table.cluster {
    .hint-input-placeholder,
    .explanation,
    .volatile {
      display: none;
    }
  }

  .left {
    color: $middlegrey;
    font-size: .875em;
    padding-left: 5px;
  }

  &.total label,
  &.typeTextField.readonly label {
    @include headerfont;
  }

  &.total .input {
    @include borderfloor;
    border-top: $borderwidth solid $verydarkgrey;
    display: flex;
    flex-direction: column-reverse;
    padding-top: 15px;
  }

  &.validationError input.type-euro,
  &.validationWarning input.type-euro {
    @include euro-sign;
  }

  &.total .input input.type-euro {
    @include euro-sign;
  }

  &.typeInterField {
    @include textblock;
    min-height: 44px;

    &.fullwidth {
      .label {
        display: none;
      }
    }

    &.feedback {
      background: url("#{$icons-img-path}attentie-wit-bg-rood.svg") 12px 16px no-repeat $superlightgrey;
      background-size: 36px 36px;
      padding-left: 60px;

      .label {
        display: none;
      }
    }

    &.validationError,
    &.skp-error,
    &.blockResults {
      background: url("#{$icons-img-path}kruis-rood.svg") 12px 12px no-repeat $superlightgrey;
      background-size: 40px 40px;
      border-color: $error;
      padding-left: 60px;

      .label {
        display: none;
      }
    }

    a {
      text-decoration: underline;
    }
  }

  &.typeTextField {
    input {
      @include focus;
      @include placeholder;
      @include textfield;
      display: inline-block;
      min-width: 2em;

      &.hasSize {
        @include font-size(14.5);
        letter-spacing: -.5px;
        width: auto;
      }

      &.type-euro {
        @include euro-sign;
      }
    }

    &.readonly input {
      background-color: $superlightgrey;
    }

    &,
    &.refresh {
      input[type="submit"],
      input.refresh {
        background: url("#{$icons-img-path}refresh.png") no-repeat 50% 50% $highlite1;
        background-size: 50% 50%;
        border-style: none;
        color: transparent;
        cursor: pointer;
        float: right;
        height: 24px !important;
        min-width: 0;
        padding: 0;
        width: 24px !important;
      }
    }

    textarea {
      @include focus;
      @include textfield(auto);
      display: block;

      &:not([data-cols]) {
        width: calc(100% - #{$formeltpadding * 2});
      }
    }

    textarea {
      line-height: 1.3;
    }

  }

  &.typeSelectionField {
    select {
      @include focus;
      @include standard-font;
      @include font-size(16);
      background-color: $white;
      background-image: none;
      border: $borderwidth solid $formbordergrey;
      border-radius: $borderradius;
      display: block;
      line-height: $line-height;
      min-height: calc(#{$line-height}em + #{$baseformpadding});
      padding: $formeltpadding;

      &:not([cols]) {
        width: 100%;
      }
    }

    datalist {
      option {
        @include standard-font;
      }
    }

    &.hidelabel {
      ul.choices {
        margin-top: 0;
      }
    }

    input[type="submit"] {
      @include buttonstyle;
      margin: 1em ($grid-gutter * 2) 0 0;

      &.selected {
        background: url("#{$icons-img-path}vink-wit-bg-transp.svg") no-repeat 0 2px $highlite1;
        background-size: 30px 30px;
        color: $white;
        padding-left: 30px;

        &:hover {
          color: $white;
        }
      }
    }

    input.othertext,
    input.selectionwidth-small,
    input.selectionwidth-medium,
    input.selectionwidth-large {
      @include textfield;
      display: inline-block;
      width: auto;
    }

    input,
    select {
      &.selectionwidth-small {
        min-width: 4em;
        width: 33%;
      }

      &.selectionwidth-medium {
        min-width: 8em;
        width: 67%;
      }

      &.selectionwidth-large {
        min-width: 12em;
        width: 100%;
      }
    }
  }

  &.typeAddressChoiceField {
    input[type="submit"] {
      @include buttonstyle;
      margin-right: $grid-gutter;
    }
  }

  ul.choices {
    display: inline-block;
    list-style: none;
    margin: 0;
    padding: 0;

    label {
      display: inline-block;
      font-weight: normal;
      margin-bottom: 0;
      padding-right: ($grid-gutter * 2.5);
      position: relative;

      input {
        margin: 0 ($grid-gutter / 2);
        position: absolute;
        top: 6px;
        transform: scale(1.2);
      }

      span {
        @include font-size(16);
        @include standard-font;
        display: inline-block;
        line-height: 1.4em;
        margin-left: 5px;
        padding-left: 25px;
        vertical-align: top;
      }
    }

    li {
      margin-top: .5em;
      min-width: 290px;
      position: relative;
      
      .explanation .inner p {
        margin-bottom: 0;
        margin-top: 0;
      }
    }
    
    &.horizontal {
      li {
        display: inline;
        line-height: $grid-gutter * 2;
        margin-right: 20px;
        padding: 5px 29px 5px 0;
      }

      label {
        display: inline-block;
        padding-right: 0;
      }

      div.input {
        display: inline;

        input {
          display: inline-block;
          top: 6px;
        }

        select {
          display: inline-block;
        }
      }
    }
  }

  &.typeDateField {
    input.type-iproxdate {
      @include textfield;
      background: url("images/icons/calendar.png") calc(100% - 5px) 50% no-repeat $white;
    }

    &,
    &.refresh {
      input[type="submit"],
      input.refresh {
        background: url("#{$icons-img-path}refresh.png") no-repeat 50% 50% $highlite1;
        background-size: 50% 50%;
        border-style: none;
        color: transparent;
        cursor: pointer;
        float: right;
        height: 24px !important;
        min-width: 0;
        padding: 0;
        width: 24px !important;
      }
    }

    .left {
      display: none;
    }
  }

  &.typeLookupField {
    input {
      @include textfield;
      margin-right: $grid-gutter;
    }
  }

  &.typeUploadField {
    position: relative;

    .input {
      margin-top: $grid-gutter * 0.5;
      vertical-align: baseline;
    }

    form {
      display: inline-block;
    }

    input[type = "file"] {
      @include focus;
      clear: both;
      overflow: hidden;
      padding: 0 1ex 0 0;

      &::file-selector-button {
        @include buttonstyle;

        background-color: $lightgrey;
      }

      + input[type="submit"] {
        color: transparent;

        &:hover {
          color: transparent !important;
        }
      }
    }

    input[type="submit"] {
      display: none;
    }

    ul {
      padding-left: 40px;
      
      li.fileRow {
        display: flex;
        list-style: disc;
        padding: 10px 0;

        &::before {
          content: "•";
          margin-left: -20px;
          position: absolute;
        }

        a {
          display: inline-block;
          overflow-wrap: anywhere;
          padding-right: 1ex;
        }

        input[type = "submit"] {
          @include buttonstyle;

          background-color: $black;
          background-image: none;
          color: transparent;
          display: inline-block;
          flex-shrink: 0;
          height: $upload-trash-button-size !important;
          line-height: $upload-trash-button-size !important;
          margin-top: 0.25em;
          -webkit-mask-position: center; // sass-lint:disable-line no-vendor-prefixes
          mask-position: center;
          mask-size: 66% !important;
          mask: url("#{$efaas-path}images/icons/trash.svg") no-repeat;
          overflow: hidden;
          padding: 0;
          width: $upload-trash-button-size;

          &:hover {
            background-color: $highlite1;
            color: transparent !important;
          }
        }
      }
    }

    .progressbar {
      .progressbarlabel {
        @include headerfont;
        color: $highlite1;
      }

      &.ui-progressbar {
        background-color: $white;
        border: $borderwidth solid $highlite1;
        height: 1.8em;
        line-height: 1.8em;
        margin: 10px 0 5px;
        position: relative;
        width: calc(300px - 1ex);

        .progressbarlabel {
          color: $highlite1;
          font-size: .875em;
          font-style: normal;
          height: 1.8em;
          line-height: 1.8em;
          position: absolute;
          text-align: center;
          text-overflow: ellipsis;
          width: calc(298px - 1ex);
          z-index: $z-base-elevated;
        }

        .ui-progressbar-value {
          background-image: url("#{$bgs-img-path}progressbar.gif");
          height: 1.8em;
        }

        &.progressbar-error {
          .ui-progressbar-value {
            display: none !important;
          }
        }
      }
    }
  }

  &.validationCompleted,
  &.validationError {
    fieldset {
      margin: 0;
    }
  }

  &.validationError,
  &.validationWarning {
    label,
    .label {
      color: $error;
    }

    &.typeTextField input,
    &.typeTextField textarea {
      @include focuserror;
      border: $borderwidth solid $error;
    }

    &.typeSelectionField select,
    &.typeSelectionField input[pattern] {
      @include focuserror;
      border: $borderwidth solid $error;
    }

    &.typeSelectionField ul.choices {
      @include focuserror;
      border: $borderwidth solid $error;
      border-radius: $borderradius;

      &.horizontal {
        padding: $grid-gutter;
      }
    }

    &.typeDateField input {
      @include focuserror;
      border: $borderwidth solid $error;
    }
  }

  .icon-error {
    display: none;
  }

  ul[role="alert"] {
    background: url("#{$bgs-img-path}bg-role-alert.png") no-repeat left top;
    border-bottom: $borderwidth solid $darkgrey;
    list-style-type: none;
    margin-left: 0;
    margin-top: -2px;
    position: absolute;
    width: 280px;

    li {
      @include headerfont;
      color: $darkgrey;
      list-style-type: none;
      padding: ($grid-gutter * 1.5) $grid-gutter ($grid-gutter * .5);
    }
  }

  &.has-teller {
    ul[role="alert"] {
      margin-top: calc(-1.5rem - 2px);
    }
  }
}

img[srcset] {
  height: auto;
  max-width: 100%;
}

table.cluster {
  border-spacing: 0;

  .formControl > fieldset > & {
    padding: 5px 20px ($grid-gutter * 2);
  }

  th {
    @include headerfont;
    padding: 0 5px 5px 0;
    text-align: left;
    vertical-align: top;
  }

  td {
    padding: 0 5px 5px 0;
    vertical-align: top;

    &:last-child {
      &,
      .formField {
        padding-right: 0;
      }
    }

    .formField {
      display: inline-block;
      padding: 5px 5px 5px 0;
      position: relative;

      ul[role="alert"] {
        background: url("#{$bgs-img-path}bg-role-alert.gif") no-repeat left top;
        margin-left: 0;
      }

      &.typeDateField {
        input.type-iproxdate {
          @include textfield;
          background: url("#{$icons-img-path}calendar.png") calc(100% - 5px) 50% no-repeat #fff;
        }
      }
    }

    label {
      @include visuallyhidden;
    }

    div.input {
      display: inline;

      input:not([type = "submit"]) {
        @include textfield;
      }

      select {
        display: inline-block;
      }

      .hint-input-placeholder {
        margin-bottom: 5px;
      }

      .label-after {
        font-family: $basetextfont;
      }
    }

    ul[role="alert"] {
      background: url("#{$bgs-img-path}bg-role-alert.gif") no-repeat left top;
      left: 0;
      white-space: normal;
    }

    input.refresh {
      margin-left: 5px;
      margin-top: 5px;
    }
  }

  .action-remove,
  + input[type="submit"] {
    @include buttonstyle;
  }

  .action-remove,
  + input[type="submit"] {
    background: url("#{$icons-img-path}min.svg") no-repeat 97% 50% $lightgrey;
    background-size: 24px 24px;
    border-radius: $borderradius;
    padding-right: 30px;

    &:hover {
      background: url("#{$icons-img-path}min-hilite.svg") no-repeat 97% 50% $lightgrey;
      background-size: 24px 24px;
      border-radius: $borderradius;
      padding-right: 30px;
    }
  }

  .action-add,
  + input[type="submit"] {
    @include buttonstyle;
  }

  .action-add,
  + input[type="submit"] {
    background: url("#{$icons-img-path}plus.svg") no-repeat 97% 50% $lightgrey;
    background-size: 24px 24px;
    border-radius: $borderradius;
    padding-right: 30px;

    &:hover {
      background: url("#{$icons-img-path}plus-hilite.svg") no-repeat 97% 50% $lightgrey;
      background-size: 24px 24px;
      border-radius: $borderradius;
      padding-right: 30px;
    }
  }

}

.process-step-control {
  legend {
    display: none;
  }

  .blockExplanation {
    padding: 0 10px;
  }

  .summary {
    padding: 10px;

    h3:first-of-type {
      margin: 0 0 .5em;
    }

    h4 {
      margin: 0 0 .5em;
    }

    .field {
      margin-bottom: .8125em;
      margin-top: .8125em;
    }

    .displayhint-hidelabel {
      min-height: 2em;
    }
  }
}

p.has-edit {
  text-align: right;
}

a.edit {
  @include buttonstyle;
}

a.edit {
  background: url("#{$icons-img-path}pen.svg") no-repeat 100% 50%;
  background-size: 30px 30px;
  border-radius: $borderradius;
  color: $highlite2;
  padding-right: 37px;
  text-decoration: none;

  &:hover {
    background: url("#{$icons-img-path}pen-hilite.svg") no-repeat 100% 50%;
    background-size: 30px 30px;
    border-radius: $borderradius;
    color: $highlite1;
    padding-right: 37px;
  }
}

.requiredStar {
  @include basefont;
  color: $error;
  padding-left: $baseformpadding;
}

.buttons {
  padding: 5px $grid-gutter ($grid-gutter * 2) $grid-gutter;
  text-align: right;

  input {
    @include buttonstyle;
    margin: 0;

    &:not(:last-child) {
      margin-right: 10px;
    }
  }

  input[value="Vorige"] {
    background: url("#{$icons-img-path}pijllinks.svg") no-repeat 0 50%;
    background-size: 30px 30px;
    border-radius: $borderradius;
    color: $black;
    padding-left: 30px;

    &:hover {
      background: url("#{$icons-img-path}pijllinks-hilite.svg") no-repeat 0 50%;
      background-size: 30px 30px;
      border-radius: $borderradius;
      color: $highlite1;
      padding-left: 30px;
    }
  }

  input[value="OK"],
  input[value="Afronden"] {
    background: url("#{$icons-img-path}vink.svg") no-repeat 100% 50% $formgrey;
    background-size: 30px 30px;
    border: $borderwidth solid $formgrey;
    border-radius: $borderradius;
    color: $black;
    padding-right: 37px;

    &:hover {
      background: url("#{$icons-img-path}vink-hilite.svg") no-repeat 100% 50% $superlightgrey;
      background-size: 30px 30px;
      border: $borderwidth solid $middlegrey;
      color: $highlite1;
      padding-right: 37px;
    }

    &.inactive,
    &:disabled {
      background: url("#{$icons-img-path}vink-hilite.svg") no-repeat 100% 50% transparent;
      background-size: 30px 30px;
      border: 2px solid transparent;
      color: $formgrey;
      cursor: default;

      &:hover {
        background: url("#{$icons-img-path}vink-hilite.svg") no-repeat 100% 50% transparent;
        background-size: 30px 30px;
        border: 2px solid transparent;
      }
    }
  }
}

.ui-datepicker {
  background: none $superlightgrey;

  .ui-widget-header {
    background: none $darkgrey;
    color: $white;
    padding: $baseformpadding ($grid-gutter * 2);
    text-align: center;

    select {
      @include focus;
      @include standard-font;
      background-color: transparent;
      border: 0 none;
      color: $white;

      option {
        background-color: $superlightgrey;
        color: $verydarkgrey;
      }
    }

    .ui-datepicker-prev {
      color: $white;
      cursor: pointer;
      display: inline-block;
      left: ($grid-gutter / 2);
      overflow: hidden;
      position: absolute;
      top: $baseformpadding;
      white-space: nowrap;
      width: 1em;

      &::before {
        content: "< ";
      }

      .ui-icon {
        display: none;
      }
    }

    .ui-datepicker-next {
      color: $white;
      cursor: pointer;
      display: inline-block;
      position: absolute;
      right: $grid-gutter;
      text-align: right;
      top: $baseformpadding;
      white-space: nowrap;
      width: 1em;

      &::after {
        content: " >";
      }

      .ui-icon {
        display: none;
      }
    }
  }

  .ui-datepicker-calendar {
    background-color: $superlightgrey;
    border: $borderwidth solid $darkgrey;
    padding: 0 $grid-gutter;
    text-align: center;

    th {
      @include headerfont;
      color: $verydarkgrey;
    }

    th,
    td {
      line-height: 2;
    }

    .ui-state-default {
      background: none !important;
      color: $black;
      padding: 0.5em;
      text-decoration: none;

      &.ui-state-hover {
        background: none $highlite1 !important;
        color: $white;
      }
    }
  }
}

@media screen and (min-width: 1024px) {
  .meldingen {
    margin-left: 280px;
  }

  .saveControl {
    display: block !important;
    float: left !important;
    margin-left: #{$blocksize + 10px} !important;
    max-width: #{$blocksize * 3 + 10px} !important;
    padding: 0 10px ($grid-gutter * 2) !important;
    position: relative !important;
    width: calc(100% - 20px) !important;
  }

  .formControl {
    display: block;
    float: left;
    max-width: #{$blocksize * 3};
    min-height: 600px;
    padding: 0 10px ($grid-gutter * 2);
    width: calc(100% - 20px);

    &#Main_ErrorBody {
      margin-left: #{$blocksize + 10px};

      > p,
      > a {
        margin: $grid-gutter;
      }
    }
  }

  .page-error,
  .page-notfound,
  .page-servicetest,
  .page-overview {
    .formControl {
      float: right;

      h2:first-of-type {
        display: none;
      }
    }
  }
}

@media screen and (max-width: 1023px) {
  .meldingen {
    margin-right: 10px;
  }

  .saveControl,
  .formControl {
    padding: 0 0 ($grid-gutter * 2);

    &#Main_ErrorBody {
      margin-left: 0;

      > p,
      > a {
        margin: $grid-gutter;
      }
    }

    h2:first-of-type {
      margin-left: 0;
      margin-right: 0;
      padding-left: 10px;
      width: calc(100% - (#{$grid-gutter * 4}));
    }
  }

  .saveControl > a {
    @include hyphenate;
    display: inline-block;
  }

  .formControl {
    legend + .block-buttons input[type="submit"] {
      background-position: 50% 50%;
      background-size: 24px 24px;
      color: transparent;
      height: 30px;
      padding-left: 0;
      padding-right: 0;
      width: 30px;

      &:hover,
      &:active {
        color: transparent !important;
      }
    }
  }

  .formField {
    label,
    .label {
      padding-right: $grid-gutter * 3;
    }

    .label-after {
      font-size: .5em;
    }
  }

  table.cluster {
    border-spacing: 0;
    max-width: 100%;
    width: auto;

    &,
    & tbody,
    & td {
      display: inline-block !important;
      width: 100% !important;

      label {
        border: 0;
        clip: auto;
        display: block;
        height: auto;
        margin: 0;
        overflow: visible;
        padding: 0;
        position: static;
        width: auto;
      }
    }

    & thead,
    & th {
      display: none;
    }

    & td {
      .formField {
        max-width: 100%;
        width: 100%;

        &.typeTextField input,
        &.typeTextField textarea {
          max-width: calc(100% - #{$grid-gutter * 2});
          width: 100%;
        }
      }

      &:last-child {
        margin-bottom: ($grid-gutter * 3);
      }
    }

  }

  img {
    height: auto;
    max-width: 270px;
  }
}

.hidden {
  display: none;
}
