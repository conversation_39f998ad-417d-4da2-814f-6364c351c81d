// tooltips/explanation formfields

.role-alert {
  color: $red;
  list-style: none;
  padding: 0;
}

.summary {
  .step {
    @include padding(0);
    position: relative;

    + .step {
      margin-top: $u2;
    }

    .block {
      @include margin($u2 0);
    }

    .has-edit {
      font-weight: bold;
      position: absolute;
      right: 1em;
      top: 0;
    }

    .field {
      @include margin($u1 0);
    }

    .fieldLabel {
      font-weight: bold;
      margin-bottom: $u1 / 2;
    }

    .fieldValue {
      thead {
        color: $grey;
      }

      ul {
        padding-left: $u3;

        li {
          list-style-type: square;
        }
      }
    }
  }
}

.submit-blocked,
.blockResults {
  border: 1px solid $red;
  padding: $u1;

  > p {
    margin: 0;
  }

  > ul {
    padding: 0;

    li {
      a {
        .fieldname::after {
          font-weight: bolder;
          margin-right: $u1 / 2;
        }
      }
    }
  }
}

.blockResults {
  margin-top: $u2;
}

.fieldsBlock {
  div.block {
    .blockResults {
      margin-top: 0;
    }
  }
}

.submit-blocked {
  @include margin(#{$u1 / 2} 0);
}
