// sass-lint:disable-all
/* csslint ignore:start */ // sass-lint: ignore
@import "../lib/normalize";
/* csslint ignore:end */ // sass-lint: ignore
// sass-lint:enable-all
@import "baselinegrid";
@import "vars";
@import "functions";
@import "fonts";
@import "forms";
@import "skiplink";

a.form-logout {
  display: none;
}

.grid-header-logo {
  .sitelogo {
    background: url("#{$assets-img-path}#{$mainlogo}") 0 50% no-repeat;
    direction: ltr;
    min-height: 90px;
    min-width: 300px;
    text-indent: -9999px;

    a {
      display: block;
      height: 90px;
      width: 300px;
    }
  }
}

.type-form-title {
  h1 {
    margin: 0;
  }
}


#Main_Status {
  border: $borderwidth solid $highlite1;
  border-radius: $borderradius;
  color: $verydarkgrey;
  margin: 0;
  min-height: 28px;
  padding: 1em .75em .25em 44px;
}

#Header_Status {
  border: $borderwidth solid transparent;
  border-radius: $borderradius;
  color: #000;
  margin: 0;
  min-height: 28px;
  padding: 1em .75em .75em 44px;
}

.logincontainer .status {
  @include basefont;
  border: $borderwidth solid transparent;
  border-radius: $borderradius;
  color: #000;
  line-height: 1.5;
  margin: .67em 0;
  min-height: 28px;
  padding: 0.5em 0.75em 0.75em 44px;

  .form-logout {
    margin-top: .5em;
  }

  &.status-digid {
    background: url("#{$logos-img-path}digid.svg") 12px 12px no-repeat $white;
    background-size: 24px 24px;
    border-color: $digid;

    .form-logout {
      display: block;
    }
  }

  &.status-eherkenning {
    background: url("#{$logos-img-path}eherkenning.svg") 12px 12px no-repeat $white;
    background-size: 24px 24px;
    border-color: $eherkenning;

    .form-logout {
      display: block;
    }
  }

  &.status-eidas {
    background: url("#{$logos-img-path}eidas.svg") 12px 12px no-repeat $white;
    background-size: 24px 24px;
    border-color: $eidas;

    .form-logout {
      display: block;
    }
  }

  .status-user-displayname {
    display: block;
  }
}

.heading-elt-nav {
  display: none;
}

.type-form-nav {

  ul {
    list-style-type: none;
    margin: ($grid-gutter * 2 + 5px) 0 ($grid-gutter * 2);
    padding: 0;

    li {
      min-height: 27px;
      padding-bottom: ($grid-gutter * 2);
    }

    ul {
      margin: 0;

      li {
        > a,
        > span {
          @include font-size(16);
          background-position: 9px 3px;
          background-size: 20px 20px;
        }

        &:last-child {
          background: url("#{$bgs-img-path}metroline.gif") 18px 0 repeat-y;
        }
      }
    }

    &#Main_FormActions {
      margin-bottom: 0;

      li {
        background-image: none;
        position: relative;

        > a,
        > span:first-child {
          line-height: 1.2em;
          min-height: 27px;
          padding-left: $nav-step-margin-left;
          padding-top: 5px;
          width: calc(100% - #{$nav-step-margin-left});

          span.step-label {
            margin-left: 0;
            margin-top: 0;
            width: 100%;
          }
        }
      }
    }

    li {
      background: url("#{$bgs-img-path}metroline-selected.png") 18px 0 repeat-y;

      > a,
      > span {
        color: $navtext;
        display: block;
        padding: 0;

        span.step-label,
        span.inner {
          @include font-size(16);
          display: inline-block;
          line-height: 1.2em;
          margin-left: $nav-step-margin-left;
          margin-top: 1px;
          min-height: 27px;
          overflow: hidden;
          width: calc(100% - #{$nav-step-margin-left});
        }
      }

      > a[class] {
        &:hover,
        &:focus {
          color: $highlite2;
          outline: 0;

          .inner {
            color: $navtext;
          }
        }

        &:focus {
          color: $highlite1;
        }
      }

      span.status {
        display: none;
      }

      &.selected {
        a.selected {
          background-position: 6px 0;
          background-size: 27px 27px;
          color: $black;

          span.step-label {
            margin-left: $nav-step-margin-left - 3px;
            margin-top: 4px;
            padding-left: 3px;
            width: auto;
          }
        }
      }

      &.validationNone {
        a.selected {
          background-image: url("#{$icons-img-path}blok-bg-dgrijs.svg");
          background-position: 6px 0;
          background-size: 27px 27px;

          span.step-label {
            margin-left: $nav-step-margin-left - 3px;
            margin-top: 5px;
            padding-left: 3px;
            width: auto;
          }
        }

        > span {
          background: url("#{$icons-img-path}blok-bg-lgrijs.svg") 9px 0 no-repeat;
          background-size: 20px 20px;
        }

        &.disabled {
          > a,
          > span {
            background-image: url("#{$icons-img-path}disabled-lgrijs-bg-witkader.svg");
            color: $middlegrey;
          }
        }
      }

      &.validationCompleted a {
        background-image: url("#{$icons-img-path}blokrand-lgrijs-bg-wit-dgrijzevink.svg");
      }

      &.validationError a {
        background: url("#{$icons-img-path}kruis.svg") 9px 0 no-repeat;
        background-size: 20px 20px;
      }

      &.validationWarning a {
        background-image: url("#{$icons-img-path}attentie-lgrijs-bg-dgrijs.svg");
      }

      .action-print {
        background: url("#{$icons-img-path}print.svg") 5px 0 no-repeat;
        background-size: 27px 27px;

        &:hover {
          background: url("#{$icons-img-path}print-hilite.svg") 5px 0 no-repeat;
          background-size: 27px 27px;
        }
      }

      .action-help {
        @include font-size(16);
        background: url("#{$icons-img-path}vraag.svg") 5px 0 no-repeat;
        background-size: 27px 27px;
        height: 30px;
        padding-left: $nav-step-margin-left;
        padding-top: 5px;

        &:hover {
          background: url("#{$icons-img-path}vraag-hilite.svg") 5px 0 no-repeat;
          background-size: 27px 27px;
        }
      }

      a.action-save {
        background: url("#{$icons-img-path}download.svg") 5px 0 no-repeat;
        background-size: 28px 28px;

        &:hover {
          background: url("#{$icons-img-path}download-hilite.svg") 5px 0 no-repeat;
          background-size: 28px 28px;
        }

        span.step-label {
          color: $black;
        }
      }

      span.action-save {
        background: url("#{$icons-img-path}download.svg") 5px 0 no-repeat;
        background-size: 28px 28px;

        span.step-label {
          color: $darkgrey;
        }

        + span {
          &::before {
            display: none;
          }

          .inner {
            display: block;
            position: relative;

            &,
            &:hover {
              color: $darkgrey;
            }
          }
        }
      }

      &:last-child {
        background: none;
      }
    }
  }

  a.action-close {
    @include basefont;
    @include font-size(16);
    background: url("#{$icons-img-path}pijllinks.svg") 10px 0 no-repeat;
    background-size: 20px 20px;
    display: inline-block;
    line-height: 1.2em;
    overflow: hidden;
    padding-left: $nav-step-margin-left;
    padding-top: 1px;
    width: calc(100% - #{$nav-step-margin-left});

    &:hover {
      background: url("#{$icons-img-path}pijllinks-hilite.svg") 10px 0 no-repeat;
      background-size: 20px 20px;
    }

    &:focus {
      color: $highlite1;
      outline: none;
    }
  }

}

@media screen and (min-width: 1001px) {

  .app-title {
    margin: 0 auto;
    width: 100%;
  }

  .row {
    position: relative;
  }

  .logincontainer {
    float: right;
    margin: 10px 0;
  }

  #Main_Status {
    left: 6px;
    margin: 0;
    position: absolute;
    top: 66px;
    width: 200px;
  }

  .type-form-nav {
    float: left;
    margin: 0;
    position: relative;
    width: #{$blocksize + 20px};
  }

  .sf-menu,
  main {
    margin: 0 auto;
    max-width: 1160px;
    width: 100%;
  }

}

@media screen and (max-width: 1000px) {

  .sf-menu,
  main {
    margin: 0 auto;
    width: 100%;
  }

  .logincontainer {
    float: none;
    margin: 0 10px 10px;
  }

  #Main_ErrorTitle {
    padding: 0 20px;
  }

  #Main_ErrorBody {
    padding: 0 10px;
  }

  .page-notfound .formControl {
    margin: 0 8px;
    width: calc(100% - 20px);
  }

  #Main_Status {
    margin: 20px 10px;
    min-height: 36px;
    padding: 10px 20px 0 60px;
  }

  .heading-elt-nav {
    @include focus;
    @include headerfont;
    background-color: transparent;
    border: 0;
    color: $highlite1;
    cursor: pointer;
    display: inline-block;
    float: left;
    font-size: 1.5em;
    margin: ($grid-gutter * 2) $grid-gutter 0;
    padding: 0;

    b.caret {
      background: url("#{$icons-img-path}hamburger.png") 0 0 repeat-y;
      display: block;
      float: left;
      height: 18px;
      margin: 5px 0 0;
      width: 25px;
    }

    span {
      display: inline-block;
      padding-left: 10px;
    }
  }

  .type-form-nav {
    display: none;

    ul {
      margin-top: ($grid-gutter * 2);
    }
  }

  .ui-dialog {
    left: 0;
    margin-left: auto;
    margin-right: auto;
    width: 280px;
  }
}

.info {
  @include textblock;
}

a.downloadLink.pdf {
  background: url("#{$icons-img-path}download.svg") 5px 0 no-repeat;
  background-size: 30px 30px;
  display: block;
  height: 30px;
  margin-left: 6px;
  padding-left: 44px;

  &:hover {
    background: url("#{$icons-img-path}download-hilite.svg") 5px 0 no-repeat;
    background-size: 30px 30px;
  }
}

a.downloadLink.pdf {
  white-space: normal;
}

.step {
  border: $borderwidth solid $formbordergrey;
  border-radius: $borderradius;
  margin: 5px 0 ($grid-gutter * 2);
  padding: 1em .75em .25em;

  h3 {
    color: $black;
    font-size: 1.313em;
  }

  h4 {
    font-size: 1.2em;
  }

  h3,
  h4 {
    &:first-child {
      margin-top: 0;
    }
  }
}

.fieldLabel {
  @include headerfont;
}

.fieldValue {
  @include basefont;
}

.summary .displayhint-total .fieldValue,
.summary .displayhint-total .fieldLabel {
  padding-top: 8px;
}

.summary .displayhint-total .fieldValue {
  border-top: $borderwidth solid $middlegrey;
  min-width: 20px;
}

.fieldValue table {
  border-spacing: 0;
}

.summary .inputtype-Euro .fieldValue,
.fieldValue .inputtype-Euro {
  min-width: 20px;
  text-align: right;
}

.summary .inputtype-Euro .fieldValue {
  padding-right: 22px;
  width: auto;
}

.fieldValue table.cluster th {
  color: $middlegrey;
  padding-bottom: 2px;
}

.fieldValue table.cluster td,
.fieldValue table.cluster th {
  padding-right: ($grid-gutter * 2);
  padding-top: 2px;
}

.fieldValue table.cluster tfoot.total th {
  border-bottom: 0;
  padding-top: 12px;

  &.inputtype-Euro {
    border-top: 2px solid $middlegrey;
  }
}

.fieldValue table.cluster tbody td.inputtype-Euro {
  @include euro-sign;
  background-position: 2px 5px;
  padding-left: #{$euro-sign-size + 7px};
}

.field.complete .fieldValue {
  margin-bottom: 5px;
}

.field.error .fieldValue {
  border: $borderwidth solid $highlite1;
  border-radius: $borderradius;
  margin-bottom: 5px;
  padding: 5px;
}

.field.inputtype-Euro {
  float: left;
  width: 100%;

  .fieldLabel {
    float: left;
    width: calc(100% - 6em - 22px);
  }

  .fieldValue {
    @include euro-sign;
    float: left;
    width: 6em;
  }
}

.displayhint-feedback {
  @include textblock;
}

.displayhint-feedback {
  background: url("#{$icons-img-path}attentie-lgrijs-bg-dgrijs.svg") 12px 16px no-repeat $superlightgrey;
  background-size: 36px 36px;
  border-color: $highlite1;
  border-radius: $borderradius;
  border-width: $borderwidth;
  margin-left: 0;
  margin-right: 0;
  min-height: 44px;
  padding-left: 60px;
}

// loader
.backdrop {
  background-color: #919191;
  display: none;
  height: 100%;
  opacity: 0;
  position: fixed;
  top: 0 !important;
  width: 100%;
  z-index: $z-backdrop !important;
}

.loader {
  display: none;
  height: 80px;
  left: calc(50vw - 40px);
  position: fixed;
  top: calc(50vh - 40px);
  width: 80px;
}

.loader::after {
  animation: loader-anim 1.2s linear infinite;
  border: 6px solid $highlite1;
  border-color: $highlite1 transparent;
  border-radius: 50%;
  content: " ";
  display: block;
  height: 64px;
  margin: 8px;
  width: 64px;
}

@keyframes loader-anim {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.ui-dialog {
  @include borderfloor;
  background-color: $white;
  border: 2px solid $highlite1;
  border-radius: $borderradius;
  color: $highlite1;
  padding-bottom: $grid-gutter;
  z-index: $z-dialog !important;

  button {
    @include buttonstyle;
  }

  button {
    padding-left: $grid-gutter;
    padding-right: $grid-gutter;
  }
}
