<?xml version="1.0" encoding="utf-8" ?>
<!--
  /**
   * (c) InfoProjects bv. - www.infoprojects.nl
   */
 -->
<xsl:stylesheet version="1.0"
  xmlns:frm="http://www.iprox.nl/ns/forms/"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:forms="urn:forms"
  exclude-result-prefixes="frm xsi forms">

  <xsl:output method="text" indent="no" />

  <xsl:include href="Util.xsl" />

  <xsl:param name="Summary_Verified" />

  <xsl:template match="/">
    <xsl:if test="frm:Form/descendant::frm:Field[contains(@Channels, 'Email')]">
      <xsl:apply-templates select="frm:Form/descendant::frm:Field[contains(@Channels, 'Email')]" mode="Summary"/>
    </xsl:if>
  </xsl:template>

  <xsl:template match="frm:Form">
    <xsl:text>Titel: </xsl:text>
    <xsl:value-of select="frm:Label"/>
    <xsl:text>&#13;&#10;</xsl:text>
    <xsl:apply-templates select="frm:Steps/frm:Step" />
  </xsl:template>

  <xsl:template match="frm:Field" mode="Summary">
    <xsl:apply-templates select="self::frm:Field" />
  </xsl:template>

  <xsl:template match="frm:Step">
    <xsl:variable name="shownBlocks" select="frm:Blocks/frm:Block[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']) and frm:Fields/frm:Field[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False'])]]" />
    <xsl:if test="$shownBlocks">
      <xsl:text>&#13;&#10;</xsl:text>
      <xsl:text>== </xsl:text>
      <xsl:value-of select="frm:Label" />
      <xsl:text> ==</xsl:text>
      <xsl:text>&#13;&#10;&#13;&#10;</xsl:text>
      <xsl:apply-templates select="$shownBlocks" />
    </xsl:if>
    <xsl:apply-templates select="frm:Steps/frm:Step" />
  </xsl:template>

  <xsl:template match="frm:Block">
    <xsl:choose>
      <xsl:when test="@xsi:type = 'FieldsBlock' and (frm:MinimumCount or frm:MaximumCount)">
        <xsl:apply-templates select="frm:Blocks/frm:Block" />
      </xsl:when>
      <xsl:otherwise>
        <xsl:variable name="containsDefaultValue">
          <xsl:choose>
            <xsl:when test="not(@xsi:type = 'SkpAddressBlock')">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field/frm:Rows/frm:Row/frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:otherwise>true</xsl:otherwise>
          </xsl:choose>
        </xsl:variable>

        <xsl:if test="not(HideTitle)">
          <xsl:text>&#13;&#10;</xsl:text>
          <xsl:text>-- </xsl:text>
          <xsl:value-of select="frm:Label" />
          <xsl:text> --</xsl:text>
          <xsl:text>&#13;&#10;&#13;&#10;</xsl:text>
        </xsl:if>

        <xsl:if test="$containsDefaultValue = 'true'">
          <xsl:text>&#13;&#10;</xsl:text>
            <xsl:value-of select="$Summary_Verified" />
          <xsl:text>&#13;&#10;</xsl:text>
        </xsl:if>

        <xsl:apply-templates select="frm:Fields/frm:Field" />
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" priority="3" />

  <xsl:template match="frm:Field">
    <xsl:value-of select="frm:Label" /><xsl:text>: </xsl:text>
    <xsl:choose>
      <xsl:when test="count(frm:Values/frm:Value) > 1">
        <xsl:text>&#13;&#10;</xsl:text>
        <xsl:for-each select="frm:Values/frm:Value">
          <xsl:text>  - </xsl:text>
          <xsl:value-of select="@Display"/>
          <xsl:text>&#13;&#10;</xsl:text>
        </xsl:for-each>
      </xsl:when>
      <xsl:when test="frm:Text">
        <xsl:apply-templates select="forms:GetXmlDoc(.)" mode="copy_html" />
      </xsl:when>
      <xsl:otherwise>
        <xsl:value-of select="frm:Values/frm:Value/@Display"/>
        <xsl:if test="../frm:LabelAfter">
          <xsl:value-of select="concat(' ', ../frm:LabelAfter)" />
        </xsl:if>
      </xsl:otherwise>
    </xsl:choose>
    <xsl:text>&#13;&#10;</xsl:text>
  </xsl:template>

  <xsl:template match="text()" mode="copy_html">
    <xsl:value-of select="." />
  </xsl:template>

  <xsl:template match="*" mode="copy_html">
    <xsl:apply-templates select="@*|node()" mode="copy_html" />
  </xsl:template>

  <xsl:template match="p|br" mode="copy_html">
    <xsl:apply-templates select="@*|node()" mode="copy_html" />
    <xsl:text>&#13;&#10;</xsl:text>
  </xsl:template>
</xsl:stylesheet>
