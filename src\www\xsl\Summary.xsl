<?xml version="1.0" encoding="utf-8" ?>
<!--
  /**
   * (c) InfoProjects bv. - www.infoprojects.nl
   *
   * $Revision: 1.19 $
   * $Date: 2012/06/05 11:18:35 $
   * $Author: Wiebe $
   */
 -->
<xsl:stylesheet version="1.0"
  xmlns:frm="http://www.iprox.nl/ns/forms/"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:overview="uri:overview"
  extension-element-prefixes="overview"
  exclude-result-prefixes="frm xsi overview">

  <xsl:output method="html" version="4.0" encoding="utf-8" indent="yes"/>

  <xsl:include href="Util.xsl" />

  <xsl:template match="/">
    <div class="summary">
      <xsl:apply-templates select="frm:Form" />
    </div>
  </xsl:template>

  <xsl:template match="frm:Form">
    <xsl:apply-templates select="frm:Steps/frm:Step[@xsi:type='InternalStep']" />
  </xsl:template>

  <xsl:template match="frm:Step">
    <xsl:variable name="shownBlocks" select="frm:Blocks/frm:Block[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOverview) or frm:Precondition/frm:Condition[@Result='False']) and frm:Fields/frm:Field[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOverview) or frm:Precondition/frm:Condition[@Result='False'])]]" />
    <xsl:if test="$shownBlocks">
      <div>
        <xsl:attribute name="class">
          <xsl:text>step</xsl:text>
          <xsl:apply-templates select="ValidationResult" />
        </xsl:attribute>

        <h3>
          <xsl:value-of select="frm:Label" />
        </h3>
        <xsl:apply-templates select="$shownBlocks" />
        <p>
          <a class="edit" href="{overview:GetStepPath(frm:UniqueID)}" title="{overview:GetResourceText('Summary_Edit')} {frm:Label}">
            <xsl:value-of select="overview:GetResourceText('Summary_Edit')" />
          </a>
        </p>
      </div>
    </xsl:if>
    <xsl:apply-templates select="frm:Steps/frm:Step" />
  </xsl:template>

  <xsl:template match="frm:Block">
    <xsl:choose>
      <xsl:when test="@xsi:type = 'FieldsBlock' and (frm:MinimumCount or frm:MaximumCount)">
        <xsl:apply-templates select="frm:Blocks/frm:Block" />
      </xsl:when>
      <xsl:otherwise>
        <xsl:variable name="containsDefaultValue">
          <xsl:choose>
            <xsl:when test="not(@xsi:type = 'SkpAddressBlock')">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field/frm:Rows/frm:Row/frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:otherwise>true</xsl:otherwise>
          </xsl:choose>
        </xsl:variable>

        <div>
          <xsl:attribute name="class">
            <xsl:text>block</xsl:text>
            <xsl:apply-templates select="ValidationResult" />
          </xsl:attribute>

          <xsl:if test="not(@HideTitle)">
            <h4>
              <xsl:value-of select="frm:Label" />
            </h4>
          </xsl:if>
          <xsl:if test="$containsDefaultValue = 'true'">
            <div>
              <xsl:value-of select="overview:GetResourceText('Summary_Verified')" />
            </div>
          </xsl:if>
          <xsl:apply-templates select="frm:Fields/frm:Field" />
        </div>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOverview) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOverview) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster-header" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOverview) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster-footer" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOverview) or frm:Precondition/frm:Condition[@Result='False']]" priority="3" />

  <xsl:template match="frm:Field">
    <div>
      <xsl:attribute name="class">
        <xsl:text>field</xsl:text>
        <xsl:apply-templates select="ValidationResult" />
        <xsl:if test="@DisplayHint">
          <xsl:value-of select="concat(' displayhint-', @DisplayHint)"/>
        </xsl:if>
        <xsl:if test="@TextInputType">
          <xsl:value-of select="concat(' inputtype-', @TextInputType)"/>
        </xsl:if>
      </xsl:attribute>
      <div class="fieldLabel">
        <xsl:value-of select="frm:Label" />
      </div>
      <div class="fieldValue">
        <xsl:apply-templates select="frm:Values" />
        <xsl:if test="@xsi:type = 'UploadField' and not(frm:Values/frm:Value)">
          <xsl:text>Er zijn geen bestanden gekozen.</xsl:text>
        </xsl:if>
        <xsl:value-of select="self::*[@xsi:type = 'InterField']/frm:Text" disable-output-escaping="yes" />
        <xsl:if test="count(frm:Rows/frm:Row) > 0" >
          <table class="cluster">
            <xsl:if test="not(@HideHeader)">
              <thead>
                <tr>
                  <xsl:apply-templates select="frm:Fields/frm:Field" mode="cluster-header" />
                </tr>
              </thead>
            </xsl:if>
            <tbody>
              <xsl:apply-templates select="frm:Rows/frm:Row/frm:Fields" mode="cluster" />
            </tbody>
            <xsl:if test="count(frm:TotalFields/frm:FieldID) > 0" >
              <tfoot class="total">
                <tr>
                  <xsl:apply-templates select="frm:Fields/frm:Field" mode="cluster-footer" />
                </tr>
              </tfoot>
            </xsl:if>
          </table>
        </xsl:if>
      </div>
    </div>
  </xsl:template>

  <xsl:template match="frm:Fields" mode="cluster">
    <tr>
      <xsl:apply-templates select="frm:Field" mode="cluster" />
    </tr>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster-header">
    <th>
      <xsl:value-of select="frm:Label" />
    </th>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster-footer">
    <th>
      <xsl:if test="@TextInputType">
        <xsl:attribute name="class">
          <xsl:value-of select="concat('inputtype-', @TextInputType)"/>
        </xsl:attribute>
      </xsl:if>
      <xsl:if test="../../frm:TotalFields/frm:FieldID[substring(., 4) = current()/frm:ID]">
        <xsl:apply-templates select="frm:Values" />
      </xsl:if>
    </th>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster">
    <td>
      <xsl:if test="@TextInputType">
        <xsl:attribute name="class">
          <xsl:value-of select="concat('inputtype-', @TextInputType)"/>
        </xsl:attribute>
      </xsl:if>
      <xsl:apply-templates select="frm:Values" />
    </td>
  </xsl:template>

  <xsl:template match="frm:Values">
    <xsl:choose>
      <xsl:when test="count(frm:Value) > 1">
        <ul>
          <xsl:for-each select="frm:Value">
            <li><xsl:value-of select="@Display"/></li>
          </xsl:for-each>
        </ul>
      </xsl:when>
      <xsl:otherwise>
        <xsl:value-of select="frm:Value/@Display"/>
        <xsl:if test="../frm:LabelAfter">
          <xsl:value-of select="concat(' ', ../frm:LabelAfter)" />
        </xsl:if>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="ValidationResult">
    <xsl:choose>
      <xsl:when test=". = 'Completed'"> complete</xsl:when>
      <xsl:when test=". = 'CompletedWithWarning'"> warning</xsl:when>
      <xsl:when test=". = 'CompletedWithError'"> error</xsl:when>
      <xsl:otherwise> <xsl:value-of select="."/></xsl:otherwise>
    </xsl:choose>
  </xsl:template>

</xsl:stylesheet>
