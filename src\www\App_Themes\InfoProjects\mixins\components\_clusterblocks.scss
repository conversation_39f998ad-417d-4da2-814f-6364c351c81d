table.cluster {
  border-spacing: 0;

  th,
  td {
    @include padding(0 #{$u1 / 2} #{$u1 / 2} 0);

    vertical-align: top;
  }

  th {
    text-align: left;
  }

  td {
    position: relative;

    .formField > label,
    .formField > span.label {
      @include hide-visually;
    }

    .action-remove {
      margin: 0;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
