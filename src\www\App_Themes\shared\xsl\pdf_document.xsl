<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet version="1.0"
  xmlns:frm="http://www.iprox.nl/ns/forms/"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:forms="urn:forms"
  xmlns="Aspose.Pdf"
  exclude-result-prefixes="frm xsi forms">

  <xsl:output method="xml" version="1.0" encoding="utf-8" indent="yes" omit-xml-declaration="yes" />

  <xsl:include href="util.xsl" />
  <xsl:include href="pdf_attributes.xsl" />

  <xsl:param name="themeFolder" />
  <xsl:param name="Summary_Verified" />

  <xsl:variable name="fullWidthThreshold" select="0" />

  <xsl:variable name="showGroupAsSeparateRows" select="'true'" />

  <xsl:template match="/">
    <Pdf xmlns="Aspose.Pdf" LineSpacing="4.0">
      <!-- Title of the document -->
      <xsl:attribute name="Title">
        <xsl:value-of select="frm:Form/frm:Label" />
      </xsl:attribute>

      <Section xsl:use-attribute-sets="text page">
        <xsl:call-template name="page-header" />

        <xsl:call-template name="info-box" />

        <xsl:call-template name="dont-send-box" />

        <Table xsl:use-attribute-sets="form-heading-table">
          <Row>
            <Cell xsl:use-attribute-sets="form-heading-cell">
              <Heading xsl:use-attribute-sets="form-heading" Level="1">
                <Segment xsl:use-attribute-sets="h0">
                  <xsl:value-of select="frm:Form/frm:Label" />
                </Segment>
              </Heading>
            </Cell>
          </Row>
        </Table>

        <Table xsl:use-attribute-sets="table">
          <xsl:apply-templates select="frm:Form/frm:Steps/frm:Step" />
        </Table>

        <xsl:call-template name="page-footer" />
      </Section>
    </Pdf>
  </xsl:template>

  <xsl:template match="frm:Step">
    <xsl:variable name="shownBlocks" select="frm:Blocks/frm:Block[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']) and frm:Fields/frm:Field[not(@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False'])]]" />
    <xsl:if test="$shownBlocks">
      <xsl:call-template name="step-before" />
      <Row xsl:use-attribute-sets="row">
        <Cell xsl:use-attribute-sets="step-heading-cell" ColumnsSpan="2">
          <xsl:call-template name="step-border" />
          <Heading xsl:use-attribute-sets="step-heading" Level="2">
            <Segment xsl:use-attribute-sets="h1">
              <xsl:value-of select="frm:Label" />
            </Segment>
          </Heading>
        </Cell>
      </Row>
      <xsl:apply-templates select="$shownBlocks" />
      <xsl:call-template name="step-after" />
    </xsl:if>
    <xsl:apply-templates select="frm:Steps/frm:Step" />
  </xsl:template>

  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster-header" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" mode="cluster-footer" priority="3" />
  <xsl:template match="*[@Excluded or @ExcludeFromSubmit and not(@ShowInOutput) or frm:Precondition/frm:Condition[@Result='False']]" priority="3" />

  <xsl:template match="frm:Block">
    <xsl:choose>
      <xsl:when test="@xsi:type = 'FieldsBlock' and (frm:MinimumCount or frm:MaximumCount)">
        <xsl:apply-templates select="frm:Blocks/frm:Block" />
      </xsl:when>
      <xsl:otherwise>
        <xsl:variable name="containsDefaultValue">
          <xsl:choose>
            <xsl:when test="not(@xsi:type = 'SkpAddressBlock')">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:when test="frm:Fields/frm:Field/frm:Rows/frm:Row/frm:Fields/frm:Field[not(@ContainsDefaultValue) and @xsi:type = 'TextField']">false</xsl:when>
            <xsl:otherwise>true</xsl:otherwise>
          </xsl:choose>
        </xsl:variable>

        <xsl:if test="not(HideTitle)">
          <Row xsl:use-attribute-sets="row">
            <Cell xsl:use-attribute-sets="cell" ColumnsSpan="2">
              <Heading xsl:use-attribute-sets="block-heading" Level="3">
                <Segment xsl:use-attribute-sets="h2">
                  <xsl:value-of select="frm:Label" />
                </Segment>
              </Heading>
            </Cell>
          </Row>
        </xsl:if>
        <xsl:if test="$containsDefaultValue = 'true'">
          <Row xsl:use-attribute-sets="row">
            <Cell xsl:use-attribute-sets="cell" ColumnsSpan="2">
              <Heading xsl:use-attribute-sets="block-heading" Level="3">
                <Segment xsl:use-attribute-sets="h2">
                  <xsl:value-of select="$Summary_Verified" />
                </Segment>
              </Heading>
            </Cell>
          </Row>
        </xsl:if>
        <xsl:apply-templates select="frm:Fields/frm:Field" />
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="frm:Field">
    <xsl:choose>
      <xsl:when test="count(frm:Rows/frm:Row) > 0 and $showGroupAsSeparateRows = 'true'">
        <xsl:apply-templates select="frm:Rows/frm:Row/frm:Fields/frm:Field" />
      </xsl:when>
      <xsl:when test="count(frm:Rows/frm:Row) > 0">
        <Row>
          <Cell xsl:use-attribute-sets="cell">
            <Border>
              <Bottom LineWidth="12" Color="White" />
            </Border>
            <Table>
              <Row xsl:use-attribute-sets="row">
                <Cell xsl:use-attribute-sets="header cell">
                  <xsl:attribute name="ColumnsSpan"><xsl:value-of select="count(frm:Fields/frm:Field)" /></xsl:attribute>
                  <Text xsl:use-attribute-sets="label text">
                    <Segment>
                      <xsl:value-of select="frm:Label" />
                    </Segment>
                  </Text>
                </Cell>
              </Row>
              <Row>
                <xsl:apply-templates select="frm:Fields/frm:Field" mode="cluster-header" />
              </Row>
              <xsl:apply-templates select="frm:Rows/frm:Row/frm:Fields" mode="cluster" />
              <xsl:if test="count(frm:TotalFields/frm:FieldID) > 0" >
                <Row>
                  <xsl:apply-templates select="frm:Fields/frm:Field" mode="cluster-footer" />
                </Row>
              </xsl:if>
            </Table>
          </Cell>
        </Row>
      </xsl:when>
      <xsl:otherwise>
        <xsl:choose>
          <xsl:when test="$fullWidthThreshold &gt; 0 and $fullWidthThreshold &lt; string-length(frm:Values/frm:Value/@Display)">
            <Row xsl:use-attribute-sets="row">
              <Cell xsl:use-attribute-sets="header cell" ColumnsSpan="2">
                <Text xsl:use-attribute-sets="label text">
                  <Segment>
                    <xsl:value-of select="frm:Label" />
                  </Segment>
                </Text>
              </Cell>
            </Row>
            <Row xsl:use-attribute-sets="row">
              <Cell xsl:use-attribute-sets="cell values" ColumnsSpan="2">
                <Border>
                  <Bottom LineWidth="6" Color="White" />
                </Border>
                <xsl:apply-templates select="." mode="field-value" />
                <Text>
                  <Segment>
                    <xsl:text>#$NL</xsl:text>
                  </Segment>
                </Text>
              </Cell>
            </Row>
          </xsl:when>
          <xsl:otherwise>
            <Row xsl:use-attribute-sets="row">
              <Cell xsl:use-attribute-sets="header cell">
                <Text xsl:use-attribute-sets="label text">
                  <Segment>
                    <xsl:value-of select="frm:Label" />
                  </Segment>
                </Text>
              </Cell>
              <Cell xsl:use-attribute-sets="cell values">
                <Border>
                  <Bottom LineWidth="6" Color="White" />
                </Border>
                <xsl:apply-templates select="." mode="field-value" />
              </Cell>
            </Row>
          </xsl:otherwise>
        </xsl:choose>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster-header">
    <Cell xsl:use-attribute-sets="cell header">
      <Text xsl:use-attribute-sets="text sublabel">
        <Segment>
          <xsl:value-of select="frm:Label" />
        </Segment>
      </Text>
    </Cell>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster-footer">
    <Cell xsl:use-attribute-sets="cell header">
      <Text xsl:use-attribute-sets="text sublabel">
        <Segment>
          <xsl:if test="../../frm:TotalFields/frm:FieldID[substring(., 4) = current()/frm:ID]">
            <xsl:apply-templates select="frm:Values/frm:Value/@Display" />
          </xsl:if>
        </Segment>
      </Text>
    </Cell>
  </xsl:template>

  <xsl:template match="frm:Fields" mode="cluster">
    <Row>
      <xsl:apply-templates select="frm:Field" mode="cluster" />
    </Row>
  </xsl:template>

  <xsl:template match="frm:Field" mode="cluster">
    <Cell xsl:use-attribute-sets="cluster-values">
      <xsl:apply-templates select="." mode="field-value" />
    </Cell>
  </xsl:template>

  <xsl:template match="frm:Field" mode="field-value">
    <xsl:apply-templates select="frm:Values" />
  </xsl:template>

  <xsl:template match="frm:Field[@xsi:type='InterField']" mode="field-value">
    <xsl:apply-templates select="frm:Text" />
  </xsl:template>

  <xsl:template match="frm:Field[frm:Rows/frm:Row]" mode="field-value" />

  <xsl:template match="frm:Values">
    <Text xsl:use-attribute-sets="text">
      <Segment>
        <xsl:choose>
          <xsl:when test="count(frm:Value) > 1">
            <xsl:for-each select="frm:Value">
              <xsl:value-of select="@Display" />
              <xsl:text>#$NL</xsl:text>
            </xsl:for-each>
          </xsl:when>
          <xsl:otherwise>
            <xsl:value-of select="frm:Value/@Display" />
            <xsl:if test="../frm:LabelAfter">
              <xsl:value-of select="concat(' ', ../frm:LabelAfter)" />
            </xsl:if>
          </xsl:otherwise>
        </xsl:choose>
      </Segment>
    </Text>
  </xsl:template>

  <xsl:template match="frm:Text">
    <Text xsl:use-attribute-sets="text">
      <Segment>
        <xsl:apply-templates select="forms:GetXmlDoc(.)" mode="copy_html" />
      </Segment>
    </Text>
  </xsl:template>

  <xsl:template match="text()" mode="copy_html">
    <xsl:value-of select="." />
  </xsl:template>

  <xsl:template match="*" mode="copy_html">
    <xsl:apply-templates select="@*|node()" mode="copy_html" />
  </xsl:template>

  <xsl:template match="p|br" mode="copy_html">
    <xsl:apply-templates select="@*|node()" mode="copy_html" />
    <xsl:text>&#13;&#10;</xsl:text>
  </xsl:template>

  <xsl:template name="page-header" />

  <xsl:template name="page-footer">
    <Footer>
      <Table ColumnWidths="12cm 5cm">
        <Row>
          <Cell>
            <Text xsl:use-attribute-sets="small small-spacing" MarginTop="0.8cm">
              <Segment>
                <xsl:value-of select="frm:Form/frm:Label" /><xsl:text> - </xsl:text>
                <xsl:call-template name="submitted">
                  <xsl:with-param name="form" select="frm:Form" />
                </xsl:call-template>
              </Segment>
            </Text>
          </Cell>
          <Cell>
            <Text Alignment="Right" Width="8cm" xsl:use-attribute-sets="small small-spacing" MarginTop="0.8cm">
              <Segment>Pagina $p van $P</Segment>
            </Text>
          </Cell>
        </Row>
      </Table>
    </Footer>
  </xsl:template>

  <xsl:template name="info-box">
    <FloatingBox xsl:use-attribute-sets="form-info-box">
      <Text xsl:use-attribute-sets="text">
        <xsl:if test="not(starts-with(frm:Form/frm:Published, '0001'))">
          <Segment xsl:use-attribute-sets="larger bold">
            <xsl:call-template name="submitted">
              <xsl:with-param name="form" select="frm:Form" />
            </xsl:call-template>
            <xsl:if test="frm:Form//frm:Field[frm:ID='Naam']/frm:Values/frm:Value/@Display">
              <xsl:text> door </xsl:text>
              <xsl:value-of select="frm:Form//frm:Field[frm:ID='Naam']/frm:Values/frm:Value/@Display" />
            </xsl:if>
          </Segment>
        </xsl:if>
        <Segment xsl:use-attribute-sets="small">
          <xsl:text>&#xA;</xsl:text>
          <xsl:value-of select="frm:Form/frm:SiteName" />
          <xsl:text> </xsl:text>
          <xsl:value-of select="frm:Form/frm:Label" />
        </Segment>
      </Text>
    </FloatingBox>
  </xsl:template>

  <xsl:template name="dont-send-box">
    <FloatingBox xsl:use-attribute-sets="form-dont-send-box">
      <Text xsl:use-attribute-sets="form-dont-send-box-text center">
        <Segment>Eigen kopie, niet insturen!</Segment>
      </Text>
    </FloatingBox>
  </xsl:template>

  <xsl:template name="step-before">
  </xsl:template>

  <xsl:template name="step-border">
    <Border>
      <Bottom LineWidth="{$divider-line-width}" Color="{$divider-color}" />
    </Border>
  </xsl:template>

  <xsl:template name="step-after">
    <Row>
      <Cell xsl:use-attribute-sets="step-after" ColumnsSpan="2">
      </Cell>
    </Row>
  </xsl:template>

  <xsl:template name="submitted">
    <xsl:param name="form" />

    <xsl:variable name="date" select="$form/frm:EventLog/frm:FormEvent[last()]/@DateTime" />

    <xsl:text>Ingediend </xsl:text>
    <xsl:call-template name="format-date">
      <xsl:with-param name="date" select="$date" />
    </xsl:call-template>
    <xsl:text> om </xsl:text>
    <xsl:call-template name="format-time">
      <xsl:with-param name="date" select="$date" />
    </xsl:call-template>
  </xsl:template>

</xsl:stylesheet>
