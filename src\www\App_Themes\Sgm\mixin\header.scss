.grid-header-logo {
  .sitelogo {
    background-size: 200px 92px;
    height: 92px;
    line-height: 0;
    margin: 24px 10px;
    min-height: auto;
    min-width: auto;
    width: 295px;

    a {
      height: 92px;
      width: 200px;
    }
  }

  .logincontainer {
    margin: 24px 10px;

    .status {
      margin: 0;
    }
  }
}

.type-form-title {
  @media screen and (min-width: 1025px) {
    margin-bottom: $grid-gutter;
  }

  .grid-element {
    h1 {
      font-size: 2.5em;
      font-weight: $fw-extrabold;
      margin: 5px 10px;
    }
  }
}

.status input.form-logout {
  appearance: none;
  background: none;
  border: 0;
  color: $highlite1;
  cursor: pointer;
  display: block;
  font-weight: bold;
  padding: 0;
}

.status.status-digid input.form-logout {
  color: $digid;
}

.status.status-eherkenning input.form-logout {
  color: $eherkenning;
}

.status.status-eidas input.form-logout {
  color: $eidas;
}
