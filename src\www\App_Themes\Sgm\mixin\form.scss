@mixin interfields {
  &.feedback,
  &.skp-error,
  &.blockResults {
    background: none;

    &::before {
      align-items: center;
      content: "";
      display: flex;
      font-family: $fa-font;
      font-size: 1.5rem;
      height: 1.5rem;
      justify-content: center;
      left: 1.25rem;
      position: absolute;
      width: 1.5rem;
    }
  }

  &.feedback {
    &::before {
      color: $highlite1;
      content: $icon-var-exclamation;
    }
  }

  &.skp-error,
  &.role-alert {
    &::before {
      color: $error;
      content: $icon-var-xmark;
    }
  }
}

.formControl {
  background-color: $highlite1-light;
  border: 0;
  margin-bottom: 0;
  margin-top: 0;
  padding-top: $grid-gutter * 0.583;
  max-width: none;

  h2:first-of-type {
    font-weight: $fw-extrabold;
    font-size: 1.75rem;
  }

  .fieldsBlock {
    padding-bottom: $grid-gutter;
  }

  @media screen and (max-width: 1024.98px) {
    h2:first-of-type,
    .fieldsBlock > fieldset > h3,
    .fieldsBlock > fieldset > legend,
    > fieldset > legend,
    .formField {
      padding-left: $grid-gutter * 0.5;
    }
  }
}

.formField {
  padding-bottom: $grid-gutter;

  @media screen and (min-width: 1025px) {
    max-width: none;
  }

  h3 + .explanation {
    margin-top: 20px;
  }

  ul.choices{
    &.horizontal {
      margin-top: 0;
    }

    label input {
      margin: 0 $grid-gutter * 0.33;
      top: 4px;
    }

    label {
      + .explanation {
        margin: 10px 0 5px 31px;

        .inner {
          background-color: transparent;
          border-left: 0;
          font-style: italic;
          padding: 0;
        }
      }
    }

    .othertext {
      width: 100%;
    }
  }

  label,
  .label {
    color: $zwart;
  }

  &.typeSelectionField input[type="submit"] {
    background-color: $highlite1;
    color: $zwart;

    &:hover {
      background-color: $highlite1-dark;
      color: $wit;
    }

    &.selected {
      background-color: $highlite1-dark;
      background-position: 0 50%;
    }
  }

  &.typeInterField {
    @include interfields;
  }

  div.explanation:focus .inner,
  div.role-tooltip:focus .inner,
  div[role="tooltip"]:focus .inner {
    right: -2px;
    width: calc(100% - #{$grid-gutter * 3 - 4});
  }

  ul[role="alert"] {
    border-bottom-color: $error;
    z-index: 2;
  }
}

.role-alert {
  @include interfields;

  position: relative;
}

.formControl .block-buttons-add,
.formControl .block-buttons-remove {
  text-align: left;

  input[type="submit"] {
    margin-left: $grid-gutter;
  }
}

.formControl .block-buttons-add input[type="submit"],
.formControl .block-buttons-remove input[type="submit"],
table.cluster .action-add,
table.cluster + input[type="submit"] {
  &,
  &:hover,
  &:focus {
    background-position-x: calc(100% - #{$grid-gutter * 0.5});
    border: 1px solid $highlite1;
    padding-right: $grid-gutter * 2;
  }
}

table.cluster {
  th {
    font-weight: normal;
  }

  td div.input {
    align-items: center;
    display: flex;

    span.label-after {
      margin-left: #{$grid-gutter * 0.33};
    }
  }
}

.buttons {
  input {
    &,
    &[value="Vorige"],
    &[value="OK"],
    &.default-button,
    &.type-submit {
      background-color: $highlite1;
      color: $zwart;

      &,
      &:hover {
        background-image: none;
        border: 0 none transparent;
        padding: 0.375rem 1.5rem;
      }

      &:hover {
        background-color: $highlite1-dark;
        color: $wit;
      }
    }

    &[value="Vorige"] {
      font-weight: 400;
    }
  }
}

.ui-datepicker {
  .ui-datepicker-header {
    background-color: $highlite1-dark;
  }

  .ui-datepicker-calendar {
    border-color: $highlite1-dark;

    .ui-state-default.ui-state-hover {
      background-color: $zwart !important;
    }
  }
}

.ui-dialog-content {
  color: $zwart;
}

.saveControl {
  margin-left: 0 !important;

  @media screen and (min-width: 1024px) {
    max-width: none !important;
  }
}