$(function() {
  initMaxLengths(
    function(maxLength) {
      return maxLength < 20;
    },
    "MaxLengthMini_Template",
    "mini"
  );
  initMaxLengths(
    function(maxLength) {
      return maxLength >= 20;
    },
    "MaxLength_Template",
    "maxi"
  );
  $("a.externLink").attr("rel", "external");
  $("a[rel=external]").attr("target", "_blank");
});

$(document).ready(function() {
  $(".type-form .grid-inside").has(".saveControl").addClass("hasSaveControl");
  $("ul.role-navigation li a.selected, ul.role-navigation li a.disabled").each(function() {
    var lv_class = $(this).attr("class");
    $(this).closest("li").addClass(lv_class);
  });
  $("ul.role-navigation li > span").each(function() {
    $(this).closest("li").addClass("disabled");
  });
  $("ul.role-menu li .action-print, ul.role-menu li .action-save").each(function() {
    $(this).wrapInner("<span class='step-label'></div>");
  });
  $(".heading-elt-nav").click(function(e) {
    e.preventDefault();
    $(".type-form-nav")
      .toggle("slow")
      .promise()
      .done(function() {
        updateNavStates(":visible");
      });
  });
  $("p:has(a.edit)").each(function() {
    $(this).addClass("has-edit");
  });
  $(".eherkenningStepText").each(function() {
    $(this).closest("fieldset").addClass("has-eherkenning");
  });
  $(".digidStepText").each(function() {
    $(this).closest("fieldset").addClass("has-digid");
  });
  $("#Main_ErrorBody").each(function() {
    $(this).closest(".formControl").addClass("has-errorbody");
  });
  // Change width value on page load
  responsive_resize();

  setupAddressPrefill();
});

// Change width value on user resize, after DOM
$(window).resize(function() {
  responsive_resize();
});

function responsive_resize() {
  var current_width = $(window).width();

  var grid_mq_medium = 999;

  if (current_width < (grid_mq_medium + 1)) {
    $("html").addClass("small").removeClass("medium").removeClass("large");
  } else if (current_width > grid_mq_medium) {
    $("html").addClass("large").removeClass("medium").removeClass("small");
    $(".type-form-nav").show();
    $(".navigation-wrapper").show();
  }

  updateNavStates(":visible");
}

// set the aria-expanded and aria-hidden attributes on the form steps
// navigation based on the visibiltiy state of the 'nav' element
function updateNavStates(state) {
  var $nav = $(".type-form-nav");
  var isExpanded = $nav.is(state);
  $("#menubutton").attr("aria-expanded", isExpanded);
  $nav.attr("aria-hidden", !isExpanded);
}

function initMaxLengths(filter, refTextAlias, classPostfix) {
  var maxLengthTemplate = "";

  function updateMaxLength() {
    var input = $(this);
    window.setTimeout(
      function() {
        var left = parseInt(input.attr("maxlength"), 10) - input.val().length;
        input
          .nextAll("div.left:first")
          .text(maxLengthTemplate.replace(/\{0\}/g, left));
      },
      0
    );
  }

  var maxLengthInputs = $(".formField:not(.typeDateField) > div.input")
    .children("input:text[maxlength],textarea[maxlength]")
    .not("[readonly]")
    .filter(function() {
      return filter(parseInt($(this).attr("maxlength"), 10));
    });
  if (maxLengthInputs.length > 0) {
    getResource(refTextAlias, function(text) {
      maxLengthTemplate = text;
      if (maxLengthTemplate) {
        maxLengthInputs
          .closest(".input")
          .append('<div class="left left-' + classPostfix + '" aria-live="polite"></div>')
          .end()
          .each(updateMaxLength)
          .on("keypress keyup change", updateMaxLength)
          .closest(".formField")
          .addClass("has-teller");
      }
    });
  }
}

function setupAddressPrefill() {
  const $postalCodeInput = $("input[autocomplete='postal-code']");
  const $fieldsBlock = $postalCodeInput.closest(".fieldsBlock");
  const $houseNumberInput = $fieldsBlock.find("label:contains('Huisnummer'), label:contains('House number')")
    .closest(".formField")
    .find("input");

  const $streetAddressInput = $("input[autocomplete='street-address']");
  const $cityInput = $("input[autocomplete='address-level2']");

  const postalCodeRegex = new RegExp($postalCodeInput.attr("pattern"));
  const houseNumberRegex = new RegExp($houseNumberInput.attr("pattern"));

  const emptyInputs = function() {
    $streetAddressInput.val("");
    $cityInput.val("");
  };

  const lookupAddress = function(postalCode, houseNumber) {
    if (postalCodeRegex.test(postalCode) && houseNumberRegex.test(houseNumber)) {
      const url = "/api/address?q=" + postalCode + "," + houseNumber;

      $.ajax({
        url: url,
        dataType: "json",
        success: function(result) {
          const address = result.response.docs.find(function(doc) {
            return doc.huisnummer && doc.huisnummer.toString() === houseNumber && doc.postcode === postalCode;
          });

          if (address) {
            $streetAddressInput.val(address.straatnaam);
            $cityInput.val(address.woonplaatsnaam);
          } else {
            emptyInputs();
          }
        },
        error: function() {
          emptyInputs();
        }
      });
    } else {
      emptyInputs();
    }
  };

  [$postalCodeInput, $houseNumberInput].forEach(function($input) {
    $input.on("blur", function() {
      if ($postalCodeInput.val() && $houseNumberInput.val()) {
        const postalCode = $postalCodeInput.val().trim().toUpperCase().replace(" ", "");
        const houseNumber = $houseNumberInput.val().trim();

        lookupAddress(postalCode, houseNumber);
      }
    });
  });
}
