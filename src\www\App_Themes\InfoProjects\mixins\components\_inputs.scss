#{$all-text-inputs} {
  @include border-color($forms-inputs-bordercolor);
  @include border-style(solid);
  @include border-width($forms-inputs-borderwidth);
  border-radius: $forms-inputs-borderradius;
}

.typeDateField input,
.typeTextField input,
textarea,
input[list],
input.othertext {
  &.selectionwidth-small {
    width: 33%;
  }

  &.selectionwidth-medium {
    width: 67%;
  }

  &.selectionwidth-large {
    width: 100%;
  }

  &::placeholder {
    color: $grey;
    opacity: 1;
  }
}

textarea {
  resize: vertical;
}

.label-after {
  margin-left: $u1;
}

input.hasSize {
  @for $i from 1 through 12 {
    &[size="#{$i}"] {
      width: #{$i * 0.95}em;
    }
  }
}
