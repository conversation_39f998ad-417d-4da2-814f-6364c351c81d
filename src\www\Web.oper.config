<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <section name="plugs" type="InfoProjects.Iprox.Forms.Xml.Configuration.PlugsSection, Iprox.Forms.Xml" />
    <section name="iprox.forms.identification" type="InfoProjects.Iprox.Forms.Identification.Connectis.Configuration.IdentificationSection, Iprox.Forms.Identification.Connectis" />
  </configSections>
  <appSettings>
    <add key="email_assembly" value="Efaas.Iprox.Forms.Xsl" />
    <add key="email_namespace" value="Efaas.Iprox.Forms.Xsl" />
    <add key="receipt_from" value="<EMAIL>" />
    <add key="Content-Security-Policy-tail" value="style-src 'self'; font-src 'self'; frame-ancestors 'self';" />
    <add key="owin:AutomaticAppStartup" value="false" />
  </appSettings>
  <connectionStrings>
  </connectionStrings>
  <plugs>
    <lookup>
      <add type="Efaas.Shared.Lookup.LookupResolver, Efaas.Shared" />
    </lookup>
    <formFiles>
      <add type="InfoProjects.Iprox.Forms.Site.FormFilesDatabase, Iprox.Forms.Core" />
    </formFiles>
    <formula>
      <add type="InfoProjects.Iprox.BackOffice.Formula.ReferenceNumber, Iprox.BackOffice.Formula" />
      <add type="Efaas.Shared.Lookup.LookupFunctions, Efaas.Shared" />
    </formula>
    <types>
      <add type="InfoProjects.Iprox.BackOffice.Forms.Xml.BackOfficeHandler, Iprox.BackOffice.Forms" />
      <add type="Efaas.Shared.Xml.KvkBusinessLocationSelect, Efaas.Shared" />
      <add type="Efaas.Shared.Xml.KvkBusinessAddressSelect, Efaas.Shared" />
    </types>
  </plugs>
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="log" type="System.Diagnostics.TextWriterTraceListener" initializeData="logfiles/trace.log" />
      </listeners>
    </trace>
  </system.diagnostics>
  <system.web>
    <webServices>
      <protocols>
        <add name="HttpGet" />
        <add name="HttpPost" />
      </protocols>
    </webServices>
    <authorization>
      <allow users="*" verbs="GET,HEAD,POST" />
      <deny users="*" />
    </authorization>
    <authentication mode="Windows" />
    <customErrors mode="On" defaultRedirect="~/data/500.htm" redirectMode="ResponseRewrite">
      <error statusCode="400" redirect="~/data/400.htm" />
      <error statusCode="401" redirect="~/data/401.htm" />
      <error statusCode="403" redirect="~/data/403.htm" />
      <error statusCode="404" redirect="~/notfound.aspx" />
      <error statusCode="503" redirect="~/data/503.htm" />
    </customErrors>
    <sessionState mode="StateServer" timeout="15" />
    <globalization uiCulture="nl-NL" culture="nl-NL" resourceProviderFactoryType="InfoProjects.Iprox.Forms.Site.FormsResourceProviderFactory" requestEncoding="utf-8" responseEncoding="utf-8" />
    <xhtmlConformance mode="Strict" />
    <httpCookies httpOnlyCookies="true" requireSSL="true" />
    <httpRuntime targetFramework="4.8" enable="true" minFreeThreads="88" minLocalRequestFreeThreads="76" maxRequestLength="1000000" executionTimeout="600" requestValidationMode="2.0" enableVersionHeader="false" />
    <pages theme="Efaas" />
  </system.web>
  <system.webServer>
    <httpErrors errorMode="Custom">
      <remove statusCode="400" />
      <error statusCode="400" path="/data/400.htm" responseMode="ExecuteURL" />
      <remove statusCode="401" />
      <error statusCode="401" path="/data/401.htm" responseMode="ExecuteURL" />
      <remove statusCode="403" />
      <error statusCode="403" path="/data/403.htm" responseMode="ExecuteURL" />
      <remove statusCode="404" subStatusCode="99" />
      <error statusCode="404" subStatusCode="99" path="/pagina-niet-gevonden" responseMode="Redirect" />
      <remove statusCode="404" />
      <error statusCode="404" path="/notfound.aspx" responseMode="ExecuteURL" />
      <remove statusCode="503" />
      <error statusCode="503" path="/data/503.htm" responseMode="ExecuteURL" />
    </httpErrors>
    <httpProtocol>
      <customHeaders>
        <clear />
        <add name="X-UA-Compatible" value="IE=edge,chrome=1" />
        <add name="X-Xss-Protection" value="1; mode=block" />
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <add name="Referrer-Policy" value="same-origin,no-referrer" />
        <add name="Permissions-Policy" value="fullscreen=(self)" />
      </customHeaders>
    </httpProtocol>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="true">
      <add name="Friendly" type="InfoProjects.Iprox.Forms.Site.FormsFriendly, Iprox.Forms.Core" />
      <add name="ScriptModule" preCondition="managedHandler" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="LogErrors" preCondition="managedHandler" type="InfoProjects.Dxe.Http.LogErrorsModule, Dxe" />
      <add name="UploadModule" type="InfoProjects.Iprox.Forms.Modules.UploadModule, Iprox.Forms.Core" />
    </modules>
    <handlers>
      <remove name="WebServiceHandlerFactory-Integrated" />
      <remove name="ScriptHandlerFactory" />
      <remove name="ScriptHandlerFactoryAppServices" />
      <remove name="ScriptResource" />
      <add name="IproxForms" verb="GET" path="IproxForms_*" type="InfoProjects.Iprox.Forms.Site.FormsPath, Iprox.Forms.Core" />
      <add name="Pdf" verb="GET" path="*.pdf" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Doc" verb="GET" path="*.doc" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Docx" verb="GET" path="*.docx" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Xls" verb="GET" path="*.xls" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Xlsx" verb="GET" path="*.xlsx" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Gif" verb="GET" path="*.gif" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Jpg" verb="GET" path="*.jpg" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Png" verb="GET" path="*.png" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="Rtf" verb="GET" path="*.rtf" type="InfoProjects.Iprox.Forms.Site.FormsContents, Iprox.Forms.Core" />
      <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptResource" verb="GET,HEAD" path="ScriptResource.axd" preCondition="integratedMode" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="UploadHandler" verb="POST" type="InfoProjects.Iprox.Forms.Handlers.UploadHandler, Iprox.Forms.Core" path="UploadHandler.ashx" />
      <add name="ProgressHandler" verb="GET" type="InfoProjects.Iprox.Forms.Handlers.ProgressHandler, Iprox.Forms.Core" path="ProgressHandler.ashx" />
      <add name="InitHandler" verb="GET" type="InfoProjects.Iprox.Forms.Handlers.InitHandler, Iprox.Forms.Core" path="InitHandler.ashx" />
      <add name="Sitemap" verb="GET" type="InfoProjects.Iprox.Forms.Modules.SitemapHandler, Iprox.Forms.Core" path="sitemap.xml" />
    </handlers>
    <defaultDocument>
      <files>
        <clear />
        <add value="overview.aspx" />
      </files>
    </defaultDocument>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="1073741824" />
      </requestFiltering>
    </security>
    <staticContent>
      <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="7.00:00:00" />
    </staticContent>
    <rewrite>
      <rules>
        <rule name="HTTP to HTTPS redirect" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="off" ignoreCase="true" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
  <location path="apps">
    <system.web>
      <authorization>
        <deny users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="data">
    <system.web>
      <authorization>
        <deny users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="logfiles">
    <system.web>
      <authorization>
        <deny users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="data/400.htm">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="data/401.htm">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="data/403.htm">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="data/404b.htm">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="data/500.htm">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <system.net>
    <mailSettings>
      <smtp>
        <network host="mail-naar-infoprojects" />
      </smtp>
    </mailSettings>
  </system.net>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="IPROX Forms LoginSoap" />
        <binding name="AuthenticatorSoap" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferSize="65536" maxBufferPoolSize="524288" maxReceivedMessageSize="65536" messageEncoding="Text" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="None">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="FileStoreSoap" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferSize="65536" maxBufferPoolSize="524288" maxReceivedMessageSize="65536" messageEncoding="Text" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="None">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
      </basicHttpBinding>
    </bindings>
    <behaviors>
      <endpointBehaviors>
        <behavior name="withMessageInspector">
          <osbEndpoint />
        </behavior>
      </endpointBehaviors>
    </behaviors>
  </system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" culture="neutral" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" culture="neutral" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="SharpCompress" culture="neutral" publicKeyToken="afb0a02973931d96" />
        <bindingRedirect oldVersion="0.0.0.0-0.32.2.0" newVersion="0.32.2.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" culture="neutral" publicKeyToken="b03f5f7f11d50a3a" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.1" newVersion="4.0.5.1" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
