.heading-elt-nav {
  margin-bottom: $grid-gutter;
  margin-top: $grid-gutter * 0.33;
}

.type-form-nav {
  ul {
    margin: $grid-gutter 0 0;

    li:not(:last-child) {
      padding-bottom: $grid-gutter;
    }

    + ul li {
      padding-bottom: $grid-gutter * 0.5;
    }

    li {
      background-image: url("#{$theme-path}images/backgrounds/metroline-selected.png");
      background-position: $grid-gutter 0;

      > a,
      > span {
        background-image: none !important;
        position: relative;

        span.step-label,
        span.inner {
          margin-top: $nav-step-margin-top;
        }

        &:hover,
        &:focus {
          background-image: none !important;
          color: $highlite1-dark;
        }

        &,
        &::before {
          &:hover,
          &:focus {
            color: $highlite1-dark;
          }
        }

        &::before {
          align-items: center;
          background-color: $wit;
          border-radius: 50%;
          border: 1px solid $highlite1;
          color: $zwart;
          content: "";
          display: flex;
          font-family: $fa-font;
          height: $nav-circle-size;
          justify-content: center;
          left: 10px;
          line-height: $nav-circle-size + 1;
          position: absolute;
          width: $nav-circle-size;
        }
      }

      > a[class] {
        &:hover,
        &:focus {
          color: $highlite1-dark;

          &::before {
            outline: 2px solid $highlite1;
          }
        }
      }

      &.selected-step {
        > a,
        > span {
          &::before {
            background-color: $highlite1;
          }

          &:hover,
          &:focus {
            &::before {
              color: $wit;
            }
          }
        }

        + .validationNone {
          > a,
          > span {
            &::before {
              background-color: $wit;
            }
          }
        }
      }

      &.validationSelected {
        > a,
        > span {
          &::before {
            background-color: $highlite1;
          }

          &:hover,
          &:focus {
            &::before {
              color: $wit;
            }
          }
        }
      }

      &.validationCompleted {
        > a,
        > span {
          &::before {
            background-color: $highlite1;
            content: "\f00c";
          }

          &:hover,
          &:focus {
            &::before {
              color: $wit;
            }
          }
        }
      }

      &.validationError {
        > a,
        > span {
          &::before {
            background-color: $wit;
            color: $error;
            content: "\f00d";
          }

          &:hover,
          &:focus {
            &::before {
              color: $highlite1-dark;
            }
          }
        }
      }

      &.validationWarning {
        > a,
        > span {
          &::before {
            content: "\21";
          }
        }
      }
    }
  }

  a.action-print {
    &::before {
      content: "\f02f";
    }

    &:hover,
    &:focus {
      color: $highlite1-dark;

      &,
      &::before {
        color: $highlite1-dark;
      }
    }
  }

  a.action-save {
    &::before {
      content: "\f0c7";
    }

    &:hover,
    &:focus {
      span.step-label {
        color: $highlite1-dark;
      }
      
      &,
      &::before {
        color: $highlite1-dark;
      }
    }
  }

  a.action-help {
    padding-left: 46px !important;
    padding-top: 0 !important;
    line-height: 26px;

    &::before {
      content: "\f128";
    }

    &:hover,
    &:focus {
      color: $highlite1-dark;

      &,
      &::before {
        color: $highlite1-dark;
      }
    }
  }

  a.action-close {
    background-image: none;
    margin-bottom: $grid-gutter;
    overflow: visible;
    padding-left: $grid-gutter * 2;
    padding-top: 6px;
    position: relative;

    &::before {
      align-items: center;
      background-color: $wit;
      border-radius: 50%;
      border: 1px solid $highlite1;
      content: "\f060";
      display: flex;
      font-family: $fa-font;
      height: $nav-circle-size;
      justify-content: center;
      left: 10px;
      line-height: $nav-circle-size + 1;
      position: absolute;
      top: 1px;
      width: $nav-circle-size;
    }

    &:hover,
    &:focus {
      color: $highlite1-dark;
    }
  }
}
