// loader
.backdrop {
  background-color: $overlay;
  display: none;
  height: 100%;
  position: fixed;
  top: 0 !important;
  width: 100%;
  z-index: 99 !important;
}

.loader {
  display: none;
  height: 80px;
  left: calc(50vw - 40px);
  position: fixed;
  top: calc(50vh - 40px);
  width: 80px;
}

.loader::after {
  animation: loader-anim 1.2s linear infinite;
  border: 6px solid $green;
  border-color: $green transparent;
  border-radius: 50%;
  content: " ";
  display: block;
  height: 64px;
  margin: 8px;
  width: 64px;
}

@keyframes loader-anim {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
