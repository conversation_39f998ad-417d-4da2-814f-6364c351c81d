// form navigation

#Main_FormNavigation,
#Main_FormActions {
  @include padding(0);

  list-style-type: none;

  li {
    @include padding(0 0 0 $u1);

    list-style: none;
    position: relative;

    a {
      position: relative;
      text-decoration: none;

      &::before {
        content: "";
        margin-left: -$u1;
        position: absolute;
        width: $u1;
      }

      &.selected {
        font-weight: bold;
      }
    }

    + li {
      @include margin($u1 0 0);
    }

    a,
    > span {
      span.step-label {
        display: inline-block;
        vertical-align: top;
      }
    }

    &.validationNone.disabled {
      cursor: not-allowed;

      .step-label {
        color: $grey;
      }
    }

    &.validationCompleted {
      a::before {
        color: $green;
        content: "V";
      }
    }

    &.validationError {
      a::before {
        color: $red;
        content: "X";
      }
    }

    > span.status {
      display: none;
    }
  }
}
