@import "vars";

@mixin buttonstyle {
  @include focus;
  @include headerfont;
  @include font-size(16);
  background-image: none;
  border: $borderwidth solid transparent;
  border-radius: $borderradius;
  color: $black;
  cursor: pointer;
  display: inline-block;
  line-height: 1.8;
  padding: $formeltpadding $grid-gutter;
  text-align: center;
  user-select: none;
  vertical-align: middle;
  white-space: nowrap;

  &:hover {
    color: $highlite2;
  }

  &.inactive {
    background-color: transparent;
    border: $borderwidth solid transparent;
    color: $darkgrey;
    cursor: default;

    &:hover {
      background-color: transparent;
    }
  }
}

@mixin textfield($fieldheight: $formeltheight) {
  @include focus;
  @include standard-font;
  @include font-size(16);
  background-color: $white;
  background-image: none;
  border: $borderwidth solid $formbordergrey;
  border-radius: $borderradius;
  color: $black;
  height: $fieldheight;
  line-height: 1.8;
  padding: $formeltpadding;
  width: calc(100% - #{$formeltpadding * 2});

  &[readonly] {
    background-color: $superlightgrey;
  }
}

@mixin focus($width: 2px) {
  &:focus {
    box-shadow: 0 0 0 $width $accent1;
    outline: none;
    transition: border-color $transitiontime;
  }
}

@mixin focuserror($width: 2px) {
  &:focus {
    box-shadow: 0 0 0 $width $verplicht;
    outline: none;
    transition: border-color $transitiontime;
  }
}

@mixin textblock {
  border: $borderwidth solid $formbordergrey;
  border-radius: $borderradius;
  margin: 5px $grid-gutter ($grid-gutter * 2);
  padding: 1em .75em .25em;

  *:first-child {
    margin-top: 0;
    padding-top: 0;
  }

  .label {
    @include font-size(18);
    margin-bottom: $grid-gutter;
  }

  .input {
    ul {
      margin-bottom: .5em;
      margin-top: .5em;
      padding-left: 40px;

      li {
        list-style-type: disc;
      }
    }

    h3,
    h4 {
      margin-bottom: 0;

      + p {
        margin-top: 0;
      }
    }

    *:first-child {
      margin-top: 0;
      padding-top: 0;
    }
  }
}

@mixin hyphenate {
  hyphens: auto;
  word-break: normal;
}

// Placeholder text
@mixin placeholder($color: $formplaceholder) {
  &::placeholder {
    color: $color;
    font-style: italic;
    opacity: 1;
  }
}

@mixin borderfloor {
  @if $borderwidth == "0" {
    border-width: 1px;
  }
  @else {
    border-width: $borderwidth;
  }
}

@mixin euro-sign {
  background: url("#{$icons-img-path}euro.svg") no-repeat 2px 50% #fff;
  background-size: $euro-sign-size $euro-sign-size;
}

@mixin visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

@mixin visually {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

.visuallyhidden,
.tekstbrowser {
  @include visuallyhidden;

  &.focusable:active,
  &.focusable:focus {
    @include visually;
  }
}

.visually {
  @include visually;
}
