.formField {
  @include margin(0 0 1em);

  .formField {
    margin: 0;
  }

  .left {
    color: $grey;
    font-size: 0.9em;
  }

  &.important {
    .label,
    label {
      &::after {
        border: 1px solid;
        content: "!";
        display: inline-block;
        font-weight: bolder;
        margin-left: 0.5em;
        padding: 0 0.5em;
        text-align: center;
      }
    }
  }

  &.feedback {
    border: 2px solid $blue;
    margin: $u1 0;
    padding: $u1;
  }

  &.skp-error {
    border: 2px solid $red;
    margin: $u1 0;
    padding: $u1;
  }
}

.cluster {
  .formField {
    .left {
      @include hide-visually;
    }
  }
}
