<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:template name="format-date">
    <xsl:param name="date" />
    <xsl:value-of select="substring($date, 9, 2)" /><xsl:text>-</xsl:text>
    <xsl:value-of select="substring($date, 6, 2)" /><xsl:text>-</xsl:text>
    <xsl:value-of select="substring($date, 1, 4)" />
  </xsl:template>

  <xsl:template name="format-time">
    <xsl:param name="date" />
    <xsl:value-of select="substring($date, 12, 5)" />
  </xsl:template>

</xsl:stylesheet>
