<?xml version="1.0" encoding="utf-8" ?>
<!-- 
  /**
   * (c) InfoProjects bv. - www.infoprojects.nl
   *
   * $Revision: 1.3 $
   * $Date: 2010/03/04 08:37:36 $
   * $Author: Wiebe $
   */
 -->
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
  <xsl:template name="date_format">
    <xsl:param name="date_value" />

    <xsl:value-of select="substring($date_value, 7, 2)" />
    <xsl:text>-</xsl:text>
    <xsl:value-of select="substring($date_value, 5, 2)" />
    <xsl:text>-</xsl:text>
    <xsl:value-of select="substring($date_value, 1, 4)" />
  </xsl:template>

  <xsl:template name="create_list">
    <xsl:param name="value" select="''" />
    <xsl:param name="separator" select="', '"/>
    <xsl:param name="escape" select="true()" />

    <xsl:variable name="temp_result">
      <xsl:call-template name="trim">
        <xsl:with-param name="text" select="$value"/>
        <xsl:with-param name="char" select="'|'"/>
      </xsl:call-template>
    </xsl:variable>
    <xsl:call-template name="search-n-replace-text">
      <xsl:with-param name="text" select="$temp_result"/>
      <xsl:with-param name="replace" select="'||'" />
      <xsl:with-param name="by" select="$separator" />
      <xsl:with-param name="escape" select="$escape" />
    </xsl:call-template>
  </xsl:template>

  <!-- template that trims chars -->
  <xsl:template name="trim">
    <xsl:param name="text"/>
    <xsl:param name="char" />
    <xsl:choose>
      <xsl:when test="string($char) = '' or string($text) = ''">
        <xsl:value-of select="$text" />
      </xsl:when>
      <xsl:when test="starts-with($text, $char)">
        <xsl:call-template name="trim">
          <xsl:with-param name="text" select="substring($text, 1 + string-length($char))" />
          <xsl:with-param name="char" select="$char" />
        </xsl:call-template>
      </xsl:when>
      <xsl:when test="substring($text, 1 + string-length($text) - string-length($char)) = $char">
        <xsl:call-template name="trim">
          <xsl:with-param name="text" select="substring($text, 1, string-length($text) - string-length($char))" />
          <xsl:with-param name="char" select="$char" />
        </xsl:call-template>
      </xsl:when>
      <xsl:otherwise>
        <xsl:value-of select="$text"/>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <!-- template that does a search & replace -->
  <xsl:template name="search-n-replace-text">
    <xsl:param name="text" />
    <xsl:param name="replace" />
    <xsl:param name="by" />
    <xsl:param name="escape" select="true()" />
    <xsl:choose>
      <xsl:when test="string($replace) = '' or string($text) = ''">
        <xsl:value-of select="$text" />
      </xsl:when>
      <xsl:when test="contains($text, $replace)">
        <xsl:value-of select="substring-before($text, $replace)" />
        <xsl:choose>
          <xsl:when test="$escape">
            <xsl:value-of select="$by" disable-output-escaping="no" />
          </xsl:when>
          <xsl:otherwise>
            <xsl:value-of select="$by" disable-output-escaping="yes" />
          </xsl:otherwise>
        </xsl:choose>
        <xsl:call-template name="search-n-replace-text">
          <xsl:with-param name="text" select="substring-after($text, $replace)"/>
          <xsl:with-param name="replace" select="$replace" />
          <xsl:with-param name="by" select="$by" />
          <xsl:with-param name="escape" select="$escape" />
        </xsl:call-template>
      </xsl:when>
      <xsl:otherwise>
        <xsl:value-of select="$text"/>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

</xsl:stylesheet>