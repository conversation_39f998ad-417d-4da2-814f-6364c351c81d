$datepicker-padding-unit: #{$u1 / 4};
$datepicker-width: 272px !default;

.ui-datepicker {
  @include padding($datepicker-padding-unit $datepicker-padding-unit 0);

  background-color: $white;
  border: 0;
  width: $datepicker-width;

  .ui-datepicker-header {
    @include border-color($grey);
    @include border-style(solid);
    @include border-width(1px 1px 0 1px);

    background: none;
  }

  .ui-datepicker-title {
    text-align: center;

    .ui-datepicker-month,
    .ui-datepicker-year {
      @include padding(0 #{$u1 * 4} 0 #{$u1 / 2});
      box-shadow: none;

      &:hover {
        background-color: $grey;
        color: $red;
        cursor: pointer;
      }
    }
  }

  .ui-icon {
    @include hide-text;
  }

  .ui-datepicker-prev,
  .ui-datepicker-next {
    line-height: 1em;

    &:hover,
    &:active {
      cursor: pointer;
    }

    &::before {
      content: "";
      display: inline-block;
    }

    span {
      @include hide-text;
    }
  }

  .ui-datepicker-next {
    float: right;

    &::before {
      content: ">";
      float: right;
      margin-left: $u1 / 2;
    }
  }

  .ui-datepicker-prev {
    float: left;

    &::before {
      content: "<";
      margin-right: $u1 / 2;
    }
  }

  .ui-datepicker-calendar {
    @include border-color($grey);
    @include border-style(solid);
    @include border-width(0 1px 1px 1px);

    width: 100%;

    tbody td {
      a {
        background: none;
        color: $black;
        display: block;
        font-weight: bold;
        padding: $datepicker-padding-unit;
        text-align: center;
        text-decoration: none;

        &:hover {
          color: $grey;
          text-decoration: underline;
        }
      }

      &.ui-datepicker-today {
        a {
          border: 1px solid $black;
        }
      }

      &.ui-datepicker-current-day {
        a {
          background-color: $grey;
          color: $white;
        }
      }
    }
  }
}
