$efaas-path: "/App_themes/Efaas/";
$theme-path: "/App_themes/Sgm/";

$img-path: "#{$efaas-path}images/";
$bgs-img-path: "#{$img-path}backgrounds/";
$buttons-img-path: "#{$img-path}buttons/";
$logos-img-path: "#{$img-path}logos/";
$sprites-img-path: "#{$img-path}sprites/";
$icons-img-path: "#{$theme-path}images/icons/";
$assets-img-path: "#{$theme-path}images/assets/";

$efaas-fonts-path: "#{$efaas-path}fonts/";
$theme-fonts-path: "#{$theme-path}fonts/";

$mainlogo: "sgm-logo.png";

$highlite1: #ef8400;
$highlite1-dark: #af5904;
$highlite1-light: #fff5e8;
$highlite2: #6e6e6e;
$accent1: #0064ad;
$accent1-light: #0096d0;
$accent2: #fff;
$accent3: #6e6e6e;
$accent4: #d9dadb;
$wit: #fff;
$zwart: #000;

$bordercolor: $highlite1;
$borderradius: 0;
$borderwidth: 1px;
$error: #c00;
$formbackground: #fff;
$formbordergrey: $highlite1;
$formeltpadding: 0.375rem;
$headercolor: #000;

$tooltip-background-color: #ffefd9;
$tooltip-border-width: 1px;
$tooltip-border-color: #ffaf47;

$digid: #c75300;
$digid-light: #fbe7d4;
$eherkenning: #e3056f;
$eherkenning-light: #fcdfed;
$eidas: #12699d;
$eidas-light: #e1f2fc;

$grid-gutter: 24px;
$grid-gutter1: 0; //was 10px
$baseblockradius: 0;

$fw-bold: 700;
$fw-extrabold: 800;

$nav-circle-size: 27px;

$upload-icon-size: 1.5rem;
$icon-var-exclamation: "\21";
$icon-var-xmark: "\f00d";

@import "../efaas/mixin/webfont";
@import "mixin/font-faces";

$basetextfont: "Open Sans", Arial, Helvetica, sans-serif;
$headertextfont: "Open Sans", Arial, Helvetica, sans-serif;
$fa-font: "Font Awesome Sharp";

@mixin headerfont {
  font-family: $headertextfont;
  font-weight: 700;
}

@mixin basefont {
  font-family: $basetextfont;
  font-weight: 400;
}

@mixin standard-font {
  @include basefont;
}

$nav-step-margin-left: 48px;
$nav-step-margin-top: 5px;

/* stylelint-disable-next-line no-invalid-position-at-import-rule -- We need this order*/
@import "../efaas/mixin/shared";

/* stylelint-disable-next-line no-invalid-position-at-import-rule -- We need this order*/
@import "mixin/functions";

/* stylelint-disable-next-line no-invalid-position-at-import-rule -- We need this order*/
@import "mixin/body";

/* stylelint-disable-next-line no-invalid-position-at-import-rule -- We need this order*/
@import "mixin/header";

/* stylelint-disable-next-line no-invalid-position-at-import-rule -- We need this order*/
@import "mixin/nav";

/* stylelint-disable-next-line no-invalid-position-at-import-rule -- We need this order*/
@import "mixin/form";

/* stylelint-disable-next-line no-invalid-position-at-import-rule -- We need this order*/
@import "mixin/process-steps";

body {
  @include basefont;

  h4 {
    @include headerfont;
    @include font-size(16);
  }

  h5 {
    @include font-size(16);
  }
}