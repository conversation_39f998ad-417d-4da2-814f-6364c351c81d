<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 30 30" style="enable-background:new 0 0 30 30;" xml:space="preserve">
<style type="text/css">
	.st0{display:none;}
	.st1{display:inline;fill:#EC0000;}
	.st2{display:inline;fill:#CCCCCC;}
	.st3{display:inline;fill:#666666;}
	.st4{display:none;fill:#EC0000;}
	.st5{fill:#CCCCCC;}
	.st6{display:inline;fill:#FFFFFF;}
	.st7{display:none;fill:#666666;}
	.st8{display:inline;}
	.st9{fill:#FFFFFF;}
	.st10{display:inline;fill:#00CC00;}
	.st11{fill:#666666;}
	.st12{fill:#EC0000;}
</style>
<g id="rood" class="st0">
	<rect x="0" y="-658" class="st1" width="30" height="30"/>
</g>
<g id="licht_grijs_1_" class="st0">
	<rect x="0" y="-748" class="st2" width="30" height="30"/>
</g>
<g id="donker_grijs" class="st0">
	<rect x="0" y="-838" class="st3" width="30" height="30"/>
</g>
<g id="donker_grijs_1_">
	<rect x="0" y="0" class="st4" width="30" height="30"/>
	<rect x="0" y="0" class="st5" width="30" height="30"/>
	<g id="rand" class="st0">
		<rect x="1" y="1" class="st6" width="28" height="28"/>
		<path class="st2" d="M28,2v26H2V2H28 M30,0H0v30h30V0L30,0z"/>
	</g>
	<rect x="0" y="0" class="st7" width="30" height="30"/>
</g>
<g id="sluit_kruis" class="st0">
	<g id="sluit_wit_copy_2" class="st8">
		<polygon class="st9" points="20.8,6.4 15.5,11.7 10.2,6.4 6.7,9.9 12,15.3 6.7,20.6 10.2,24.1 15.5,18.8 20.8,24.1 24.3,20.6 
			19,15.3 24.3,9.9 		"/>
	</g>
	<g id="sluit_rood">
		<polygon class="st1" points="20.8,6.7 15.5,12 10.2,6.7 6.7,10.2 12,15.5 6.7,20.8 10.2,24.3 15.5,19 20.8,24.3 24.3,20.8 
			19,15.5 24.3,10.2 		"/>
	</g>
	<g id="sluit_lgrijs">
		<polygon class="st2" points="20.8,6.7 15.5,12 10.2,6.7 6.7,10.2 12,15.5 6.7,20.8 10.2,24.3 15.5,19 20.8,24.3 24.3,20.8 
			19,15.5 24.3,10.2 		"/>
	</g>
	<g id="sluit_dgrijs">
		<polygon class="st3" points="20.8,6.7 15.5,12 10.2,6.7 6.7,10.2 12,15.5 6.7,20.8 10.2,24.3 15.5,19 20.8,24.3 24.3,20.8 
			19,15.5 24.3,10.2 		"/>
	</g>
</g>
<g id="plus_wit" class="st0">
	<polygon class="st6" points="25.5,-257.3 18,-257.3 18,-264.8 13,-264.8 13,-257.3 5.5,-257.3 5.5,-252.3 13,-252.3 13,-244.8 
		18,-244.8 18,-252.3 25.5,-252.3 	"/>
</g>
<g id="plus_wit_copy" class="st0">
	<polygon class="st6" points="25.5,-77.3 18,-77.3 18,-84.8 13,-84.8 13,-77.3 5.5,-77.3 5.5,-72.3 13,-72.3 13,-64.8 18,-64.8 
		18,-72.3 25.5,-72.3 	"/>
</g>
<g id="plus_wit_copy_2" class="st0">
	<polygon class="st6" points="25.5,12.8 18,12.8 18,5.3 13,5.3 13,12.8 5.5,12.8 5.5,17.8 13,17.8 13,25.3 18,25.3 18,17.8 
		25.5,17.8 	"/>
</g>
<g id="plus_rood" class="st0">
	<polygon class="st1" points="25.5,-377.3 18,-377.3 18,-384.8 13,-384.8 13,-377.3 5.5,-377.3 5.5,-372.3 13,-372.3 13,-364.8 
		18,-364.8 18,-372.3 25.5,-372.3 	"/>
</g>
<g id="plus_licht_grijs" class="st0">
	<polygon class="st2" points="25.5,-467.3 18,-467.3 18,-474.8 13,-474.8 13,-467.3 5.5,-467.3 5.5,-462.3 13,-462.3 13,-454.8 
		18,-454.8 18,-462.3 25.5,-462.3 	"/>
</g>
<g id="plus_licht_grijs_copy" class="st0">
	<polygon class="st2" points="25.5,-167.3 18,-167.3 18,-174.8 13,-174.8 13,-167.3 5.5,-167.3 5.5,-162.3 13,-162.3 13,-154.8 
		18,-154.8 18,-162.3 25.5,-162.3 	"/>
</g>
<g id="plus_donker_grijs" class="st0">
	<polygon class="st3" points="25.5,-557.3 18,-557.3 18,-564.8 13,-564.8 13,-557.3 5.5,-557.3 5.5,-552.3 13,-552.3 13,-544.8 
		18,-544.8 18,-552.3 25.5,-552.3 	"/>
</g>
<g id="min_wit" class="st0">
	<rect x="5.5" y="12.8" class="st6" width="20" height="5"/>
</g>
<g id="min_rood" class="st0">
	<rect x="5.5" y="12.8" class="st1" width="20" height="5"/>
</g>
<g id="min_licht_grijs" class="st0">
	<rect x="5.5" y="12.8" class="st2" width="20" height="5"/>
</g>
<g id="min_donker_grijs" class="st0">
	<rect x="5.5" y="12.8" class="st3" width="20" height="5"/>
</g>
<g id="pijl_rechts_wit" class="st0">
	<polygon class="st6" points="12.2,25.3 8.6,21.7 15.3,15 9,8.8 12.6,5.3 22.4,15 	"/>
</g>
<g id="pijl_rechts_rood" class="st0">
	<polygon class="st1" points="12.2,25.3 8.6,21.7 15.3,15 9,8.8 12.6,5.3 22.4,15 	"/>
</g>
<g id="pijl_rechts_licht_grijs" class="st0">
	<polygon class="st2" points="12.2,25.3 8.6,21.7 15.3,15 9,8.8 12.6,5.3 22.4,15 	"/>
</g>
<g id="pijl_rechts_donker_grijs" class="st0">
	<polygon class="st3" points="12.2,25.3 8.6,21.7 15.3,15 9,8.8 12.6,5.3 22.4,15 	"/>
</g>
<g id="pijl_links_wit" class="st0">
	<polygon class="st6" points="18.8,25.3 22.4,21.7 15.7,15 22,8.8 18.4,5.3 8.6,15 	"/>
</g>
<g id="pijl_links_rood" class="st0">
	<polygon class="st1" points="18.8,25.3 22.4,21.7 15.7,15 22,8.8 18.4,5.3 8.6,15 	"/>
</g>
<g id="pijl_links_licht_grijs" class="st0">
	<polygon class="st2" points="18.8,25.3 22.4,21.7 15.7,15 22,8.8 18.4,5.3 8.6,15 	"/>
</g>
<g id="pijl_links_donker_grijs" class="st0">
	<polygon class="st3" points="18.8,25.3 22.4,21.7 15.7,15 22,8.8 18.4,5.3 8.6,15 	"/>
</g>
<g id="vink_rood" class="st0">
	<polygon class="st1" points="13.1,22.9 5.9,15.6 9.1,12.5 13.1,16.5 22.8,6.8 26,10 	"/>
</g>
<g id="vink_wit" class="st0">
	<polygon class="st6" points="13.1,22.9 5.9,15.6 9.1,12.5 13.1,16.5 22.8,6.8 26,10 	"/>
</g>
<g id="vink_groen" class="st0">
	<polygon class="st10" points="13.1,22.9 5.9,15.6 9.1,12.5 13.1,16.5 22.8,6.8 26,10 	"/>
</g>
<g id="vink_licht_grijs" class="st0">
	<polygon class="st2" points="13.1,22.9 5.9,15.6 9.1,12.5 13.1,16.5 22.8,6.8 26,10 	"/>
</g>
<g id="vink_donkergrijs" class="st0">
	<polygon class="st3" points="13.1,22.9 5.9,15.6 9.1,12.5 13.1,16.5 22.8,6.8 26,10 	"/>
</g>
<g id="pen_donkergrijs" class="st0">
	<g class="st8">
		<path class="st11" d="M25.2,8.3L23,6.1c-0.2-0.2-0.5-0.3-0.8-0.3c-0.3,0-0.6,0.1-0.8,0.3l-1.8,1.8l3.8,3.8l1.8-1.8
			C25.7,9.5,25.7,8.8,25.2,8.3L25.2,8.3z M25.2,8.3"/>
		<path class="st11" d="M8.2,19.3l3.8,3.8l11.5-11.4l-3.8-3.8L8.2,19.3z M8.2,19.3"/>
		<path class="st11" d="M7.4,20.1l-0.8,4.7l4.7-0.8L7.4,20.1z M7.4,20.1"/>
	</g>
</g>
<g id="pen_wit" class="st0">
	<g class="st8">
		<path class="st9" d="M25.2,8.3L23,6.1c-0.2-0.2-0.5-0.3-0.8-0.3c-0.3,0-0.6,0.1-0.8,0.3L8.2,19.3l3.8,3.8L25.2,9.9
			C25.7,9.5,25.7,8.8,25.2,8.3L25.2,8.3z M25.2,8.3"/>
		<path class="st9" d="M7.4,20.1l-0.8,4.7l4.7-0.8L7.4,20.1z M7.4,20.1"/>
	</g>
</g>
<g id="pen_donkergrijs_1_" class="st0">
	<g class="st8">
		<path class="st11" d="M25.2,8.3L23,6.1c-0.2-0.2-0.5-0.3-0.8-0.3c-0.3,0-0.6,0.1-0.8,0.3L8.2,19.3l3.8,3.8L25.2,9.9
			C25.7,9.5,25.7,8.8,25.2,8.3L25.2,8.3z M25.2,8.3"/>
		<path class="st11" d="M7.4,20.1l-0.8,4.7l4.7-0.8L7.4,20.1z M7.4,20.1"/>
	</g>
</g>
<g id="pen_lichtgrijs" class="st0">
	<g class="st8">
		<path class="st5" d="M25.2,8.3L23,6.1c-0.2-0.2-0.5-0.3-0.8-0.3c-0.3,0-0.6,0.1-0.8,0.3L8.2,19.3l3.8,3.8L25.2,9.9
			C25.7,9.5,25.7,8.8,25.2,8.3L25.2,8.3z M25.2,8.3"/>
		<path class="st5" d="M7.4,20.1l-0.8,4.7l4.7-0.8L7.4,20.1z M7.4,20.1"/>
	</g>
</g>
<g id="pen_rood_1_" class="st0">
	<g class="st8">
		<path class="st12" d="M25.2,8.3L23,6.1c-0.2-0.2-0.5-0.3-0.8-0.3c-0.3,0-0.6,0.1-0.8,0.3L8.2,19.3l3.8,3.8L25.2,9.9
			C25.7,9.5,25.7,8.8,25.2,8.3L25.2,8.3z M25.2,8.3"/>
		<path class="st12" d="M7.4,20.1l-0.8,4.7l4.7-0.8L7.4,20.1z M7.4,20.1"/>
	</g>
</g>
<g id="vraag_wit" class="st0">
	<g class="st8">
		<path class="st9" d="M14.9,19.5v-1.5c0-1.7,0.4-2.1,1.5-3.3l2.2-2.2c0.5-0.5,0.7-1.1,0.7-1.8c0-1.5-1.1-2.5-2.5-2.5
			c-1.6,0-2.7,1.2-2.8,2.7l-3.6-0.3c0.4-3.6,3-5.7,6.5-5.7c3.3,0,5.9,1.9,5.9,5.5c0,2.4-1.4,3.6-3.1,5.2c-1,1-1.5,1.3-1.5,2.8v1.1
			H14.9z M16.6,21.2c1.2,0,2.2,0.9,2.2,2.1s-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S15.3,21.2,16.6,21.2z"/>
	</g>
</g>
<g id="vraag_rood" class="st0">
	<g class="st8">
		<path class="st12" d="M14.9,19.5v-1.5c0-1.7,0.4-2.1,1.5-3.3l2.2-2.2c0.5-0.5,0.7-1.1,0.7-1.8c0-1.5-1.1-2.5-2.5-2.5
			c-1.6,0-2.7,1.2-2.8,2.7l-3.6-0.3c0.4-3.6,3-5.7,6.5-5.7c3.3,0,5.9,1.9,5.9,5.5c0,2.4-1.4,3.6-3.1,5.2c-1,1-1.5,1.3-1.5,2.8v1.1
			H14.9z M16.6,21.2c1.2,0,2.2,0.9,2.2,2.1s-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S15.3,21.2,16.6,21.2z"/>
	</g>
</g>
<g id="vraag_lichtgrijs" class="st0">
	<g class="st8">
		<path class="st5" d="M14.9,19.5v-1.5c0-1.7,0.4-2.1,1.5-3.3l2.2-2.2c0.5-0.5,0.7-1.1,0.7-1.8c0-1.5-1.1-2.5-2.5-2.5
			c-1.6,0-2.7,1.2-2.8,2.7l-3.6-0.3c0.4-3.6,3-5.7,6.5-5.7c3.3,0,5.9,1.9,5.9,5.5c0,2.4-1.4,3.6-3.1,5.2c-1,1-1.5,1.3-1.5,2.8v1.1
			H14.9z M16.6,21.2c1.2,0,2.2,0.9,2.2,2.1s-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S15.3,21.2,16.6,21.2z"/>
	</g>
</g>
<g id="vraag_donkergrijs" class="st0">
	<g class="st8">
		<path class="st11" d="M14.9,19.5v-1.5c0-1.7,0.4-2.1,1.5-3.3l2.2-2.2c0.5-0.5,0.7-1.1,0.7-1.8c0-1.5-1.1-2.5-2.5-2.5
			c-1.6,0-2.7,1.2-2.8,2.7l-3.6-0.3c0.4-3.6,3-5.7,6.5-5.7c3.3,0,5.9,1.9,5.9,5.5c0,2.4-1.4,3.6-3.1,5.2c-1,1-1.5,1.3-1.5,2.8v1.1
			H14.9z M16.6,21.2c1.2,0,2.2,0.9,2.2,2.1s-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S15.3,21.2,16.6,21.2z"/>
	</g>
</g>
<g id="attentie_wit" class="st0">
	<g class="st8">
		<g>
			<path class="st9" d="M16,21.2c1.2,0,2.2,0.9,2.2,2.1c0,1.2-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S14.8,21.2,16,21.2z M17.7,19.1
				h-3.4V5.5h3.4V19.1z"/>
		</g>
	</g>
</g>
<g id="attentie_rood" class="st0">
	<g class="st8">
		<g>
			<path class="st12" d="M16,21.2c1.2,0,2.2,0.9,2.2,2.1c0,1.2-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S14.8,21.2,16,21.2z M17.7,19.1
				h-3.4V5.5h3.4V19.1z"/>
		</g>
	</g>
</g>
<g id="attentie_lgrijs" class="st0">
	<g class="st8">
		<g>
			<path class="st5" d="M16,21.2c1.2,0,2.2,0.9,2.2,2.1c0,1.2-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S14.8,21.2,16,21.2z M17.7,19.1
				h-3.4V5.5h3.4V19.1z"/>
		</g>
	</g>
</g>
<g id="attentie_dgrijs" class="st0">
	<g class="st8">
		<g>
			<path class="st11" d="M16,21.2c1.2,0,2.2,0.9,2.2,2.1c0,1.2-1,2.2-2.2,2.2c-1.2,0-2.2-0.9-2.2-2.1S14.8,21.2,16,21.2z M17.7,19.1
				h-3.4V5.5h3.4V19.1z"/>
		</g>
	</g>
</g>
<g id="guides">
</g>
</svg>
